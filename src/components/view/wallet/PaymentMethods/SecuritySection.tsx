import React from "react";
import { <PERSON>, <PERSON>, Check } from "lucide-react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/common/ui/card";
import { Separator } from "@/components/common/ui/separator";

export const SecuritySection: React.FC = () => {
  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader>
        <CardTitle className="text-white text-sm">Security & Privacy</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center gap-3">
          <Landmark className="w-5 h-5 text-green-400 mt-0.5" />
          <div className="w-full flex flex-row justify-between">
            <p className="text-white font-medium text-sm">
              Bank-level Security
            </p>
            <p className="text-zinc-400 text-xs">
              All payment information is encrypted and stored securely
            </p>
          </div>
        </div>

        <Separator className="bg-zinc-700" />

        <div className="flex items-center gap-3">
          <Check className="w-5 h-5 text-green-400 mt-0.5" />
          <div className="w-full flex flex-row justify-between">
            <p className="text-white font-medium text-sm">PCI Compliant</p>
            <p className="text-zinc-400 text-xs">
              We follow industry standards for payment processing
            </p>
          </div>
        </div>

        <Separator className="bg-zinc-700" />

        <div className="flex items-center gap-3">
          <Shield className="w-5 h-5 text-green-400 mt-0.5" />
          <div className="w-full flex flex-row justify-between">
            <p className="text-white font-medium text-sm">Fraud Protection</p>
            <p className="text-zinc-400 text-xs">
              Advanced monitoring protects against unauthorized transactions
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
