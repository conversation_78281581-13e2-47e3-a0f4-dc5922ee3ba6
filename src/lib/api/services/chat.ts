import { BaseService } from "./base";
import { prisma } from "@/lib/common/prisma";

import {
  CreateRoomSchema,
  CreateMessageSchema,
  type CreateRoom,
  type CreateMessage,
  type UserState,
} from "../validators/schemas/chat";

import { DocumentService } from "./document";

// Chat statistics type
interface ChatStatistics {
  totalRooms: number;
  totalMessages: number;
  activeUsers: number;
  onlineUsers: number;
}

export class ChatService extends BaseService {
  private requiredAuth: boolean;

  // In-memory state for real-time features (typing indicators, user states)
  private userStates: Map<string, UserState> = new Map();
  private typingUsers: Map<string, Set<string>> = new Map(); // roomId -> Set of userIds

  constructor({ requiredAuth = true }: { requiredAuth?: boolean } = {}) {
    super();

    this.requiredAuth = requiredAuth;
    this.setModel(prisma.room as any);
  }

  /**
   * Get all rooms for a user
   */
  async getRooms(userId?: string): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Fetching chat rooms", { userId });

      // Get current user's account if userId not provided
      const { id: targetUserId } = await this.getCurrentUserAccount();

      if (!targetUserId) {
        throw new Error("User ID is required");
      }

      // Query rooms where user is a member
      const rooms = await this.findManyRecords({
        where: {
          OR: [
            { accountId: targetUserId },
            { members: { some: { accountId: targetUserId } } },
          ],
        },
        include: {
          members: true,
          messages: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
            include: {
              attachments: true,
            },
          },
          contract: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      // Transform to match expected format
      const enrichedRooms = rooms.map((room: any) => ({
        ...room,
        lastMessage: room.messages[0] || null,
        unreadCount: 0, // TODO: Implement unread count logic
      }));

      this.log("info", `Found ${enrichedRooms.length} rooms`);

      return enrichedRooms;
    }, "getRooms");
  }

  async enrichMessageWithSender(messages: any[]) {
    this.setModel(prisma.account as any);

    return messages.map(async (message: any) => {
      const account = await this.findUniqueRecord({
        where: { id: message.sent_from },
        select: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      });

      return {
        ...message,
        sender: (account as any)?.user || null,
      };
    });
  }

  async enrichMessageWithAttachments(messages: any[]) {
    this.setModel(prisma.document as any);

    return messages.map(async (message: any) => {
      const attachments = await this.findManyRecords({
        where: { association_id: message.id, association_entity: "message" },
        select: {
          id: true,
          name: true,
          file_type: true,
          path: true,
        },
      });

      let remapedAttachments = attachments.map((attachment: any) => ({
        content: attachment,
        entity: "attachment",
        id: attachment.id,
      }));

      return {
        ...message,
        associations: [...message.associations, ...remapedAttachments],
      };
    });
  }

  /**
   * Get messages for a room
   */
  async getMessages(roomId: string, limit = 50, offset = 0): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Fetching messages for room: ${roomId}`, {
        limit,
        offset,
      });

      const originalModel = this.model || (prisma.room as any);

      try {
        // Verify user has access to this room
        const currentAccount = await this.getCurrentUserAccount();

        // Set model to member for access check
        this.setModel(prisma.member as any);
        const roomAccess = await this.findFirstRecord({
          where: {
            roomId: roomId,
            accountId: currentAccount.id,
          },
        });

        if (!roomAccess) {
          throw new Error("Access denied to this room");
        }

        // Set model to message for querying messages
        this.setModel(prisma.message as any);
        const messages = await this.findManyRecords({
          where: { roomId: roomId },
          orderBy: { createdAt: "asc" },
        });

        // Set model to account for enriching with sender info
        let enrichedSenders = await Promise.all(
          await this.enrichMessageWithSender(messages)
        );
        let enrichedAttachments = await Promise.all(
          await this.enrichMessageWithAttachments(enrichedSenders)
        );

        let enrichedMessages = enrichedAttachments;

        this.log("info", `Found ${enrichedMessages.length} room messages`);

        return enrichedMessages;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "getMessages");
  }

  /**
   * Send a message
   */
  async sendMessage(messageData: CreateMessage): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Sending message", { roomId: messageData.roomId });

      let attachments: Record<string, any>[] = [];
      let messages: Record<string, any> = {};

      Object.entries(messageData).forEach(
        ([key, value]: [string, any], index: number) => {
          if (key.startsWith("attachment-")) {
            attachments.push({ ["file-" + index]: value });
          } else messages[key] = value;
        }
      );

      // Validate input
      const validatedData = CreateMessageSchema.parse(messages);

      // Ensure associations is an array
      const messageToCreate = {
        ...validatedData,
        associations: validatedData.associations || [],
      };

      const originalModel = this.model || (prisma.room as any);

      try {
        // Verify user has access to the room
        if (messageToCreate.roomId) {
          const currentAccount = await this.getCurrentUserAccount();

          // Set model to member for access check
          this.setModel(prisma.member as any);
          const roomAccess = await this.findFirstRecord({
            where: {
              roomId: messageToCreate.roomId,
              accountId: currentAccount.id,
            },
          });

          if (!roomAccess) {
            throw new Error("Access denied to this room");
          }
        }

        // Set model to message for creating message
        this.setModel(prisma.message as any);
        const newMessage: { id: string } = await this.createRecord({
          data: messageToCreate,
          select: { id: true },
        });

        if (attachments && attachments?.length > 0) {
          // Set model to attachment for creating attachments
          let documentsWithAssociations = attachments.map((file: any) => ({
            ...file,
            category: "messages",
            association_entity: "message",
            association_id: newMessage?.id || "",
          }));

          let document = new DocumentService({
            requireAuth: false,
          });

          document.bulkCreateDocuments(documentsWithAssociations);
        }

        // Update room's updatedAt timestamp
        if (messageToCreate.roomId) {
          // Set model back to room for updating
          this.setModel(prisma.room as any);
          await this.updateRecord({
            where: { id: messageToCreate.roomId },
            data: { updatedAt: new Date() },
          });
        }

        this.log("info", `Message sent: ${(newMessage as any).id}`);

        return newMessage;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "sendMessage");
  }

  /**
   * Create a new room
   */
  async createRoom(roomData: CreateRoom): Promise<any> {
    try {
      this.log("info", "Creating new room", { name: roomData.name });

      // Validate input
      const validatedData: any = CreateRoomSchema.parse(roomData);

      const currentAccount: any = await this.getCurrentUserAccount();

      if (!currentAccount) {
        throw new Error("Unauthorized");
      }

      validatedData.accountId = currentAccount.id;

      // Add members to room
      let room: any = await this.createRecord({
        data: validatedData,
        select: {
          id: true,
          name: true,
          about: true,
          contractId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      this.log("info", `Room created: ${room.name}`);

      return this.createSuccessResponse(room, 201, "Room created successfully");
    } catch (error) {
      this.log("error", `Error creating room: ${error}`);
      return this.createErrorResponse("Failed to create room", 500);
    }
  }

  /**
   * Update user state (online/offline/typing/away)
   */
  async updateUserState(
    userId: string,
    state: UserState,
    roomId?: string
  ): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Updating user state: ${userId} -> ${state}`, {
        roomId,
      });

      // Update in-memory user state for real-time features
      this.userStates.set(userId, state);

      // Handle typing state in memory (for real-time typing indicators)
      if (state === "typing" && roomId) {
        if (!this.typingUsers.has(roomId)) {
          this.typingUsers.set(roomId, new Set());
        }
        this.typingUsers.get(roomId)!.add(userId);
      } else if (roomId && this.typingUsers.has(roomId)) {
        this.typingUsers.get(roomId)!.delete(userId);
      }

      // Note: Member state persistence could be added here if needed
      // For now, we keep user states in memory for real-time features

      this.log("info", `User state updated: ${userId}`);

      return { userId, state, roomId };
    }, "updateUserState");
  }

  /**
   * Get typing users for a room
   */
  async getTypingUsers(roomId: string): Promise<any> {
    return this.executeOperation(async () => {
      const typingUserIds = Array.from(this.typingUsers.get(roomId) || []);

      if (typingUserIds.length === 0) {
        return [];
      }

      const originalModel = this.model || (prisma.room as any);

      try {
        // Set model to account for querying user info
        this.setModel(prisma.account as any);
        const typingUsers = await this.findManyRecords({
          where: {
            id: {
              in: typingUserIds,
            },
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        });

        return (typingUsers as any[])
          .map((account) => account.user)
          .filter(Boolean);
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "getTypingUsers");
  }

  /**
   * Get chat statistics
   */
  async getStatistics(): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Fetching chat statistics");

      const originalModel = this.model || (prisma.room as any);

      try {
        // Get room count
        this.setModel(prisma.room as any);
        const totalRooms = await this.countRecords({});

        // Get message count
        this.setModel(prisma.message as any);
        const totalMessages = await this.countRecords({});

        // Count active users from in-memory state
        const activeUsers = Array.from(this.userStates.values()).filter(
          (state) => state !== "offline"
        ).length;

        const onlineUsers = Array.from(this.userStates.values()).filter(
          (state) => state === "online"
        ).length;

        const stats: ChatStatistics = {
          totalRooms,
          totalMessages,
          activeUsers,
          onlineUsers,
        };

        return stats;
      } finally {
        // Restore original model
        this.setModel(originalModel);
      }
    }, "getStatistics");
  }
}
