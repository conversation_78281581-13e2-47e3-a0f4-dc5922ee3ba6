"use client";

/**
 * Settings Context
 *
 * Provides a centralized context for all settings-related functionality,
 * integrating RBAC hooks and other settings state management.
 */

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from "react";
import {
  useRBAC,
  usePermissions,
  useUserProfile,
  useUserManagement,
  useRoleManagement,
} from "@/hooks/useRBAC";

interface NotificationPreferences {
  email: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
    weeklyDigest: boolean;
  };
  push: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
  };
  inApp: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
    roleChanges: boolean;
  };
}

interface SettingsContextValue {
  // RBAC functionality
  rbac: {
    users: any[];
    roles: any[];
    currentUser: any;
    isLoading: boolean;
    error?: string;
    refreshData: () => void;
    initializeRBAC: () => void;
  };

  // Permissions
  permissions: {
    // Legacy permission functions (backward compatibility)
    hasPermission: (permission: string) => boolean;
    hasAnyPermission: (permissions: string[]) => boolean;
    hasAllPermissions: (permissions: string[]) => boolean;

    // New entity-based permission functions
    hasEntityPermission: (entity: string, action: string) => boolean;
    hasAnyEntityPermission: (entity: string, actions: string[]) => boolean;
    hasAllEntityPermissions: (entity: string, actions: string[]) => boolean;

    // User data and convenience flags
    userRole: any;
    canManageUsers: boolean;
    canManageRoles: boolean;
    canManageDocuments: boolean;
    canManageProposals: boolean;
    canViewReports: boolean;
    isAdmin: boolean;
  };

  // Profile management
  profile: {
    profile: any;
    currentUser: any;
    isLoading: boolean;
    updateUserProfile: (profileData: any) => Promise<any>;
    upsertProfile: (profileData: any) => Promise<any>;
    hasProfile: boolean;
  };

  // User management
  userManagement: {
    users: any[];
    assignRoleToUser: (userId: string, roleId: string) => Promise<any>;
    removeRoleFromUser: (userId: string) => Promise<any>;
    getUsersWithoutRole: () => any[];
    getUsersWithRole: (roleId: string) => any[];
  };

  // Role management
  roleManagement: {
    roles: any[];
    createRole: (
      name: string,
      permissions?: Record<string, string[]>
    ) => Promise<any>;
    updateRole: (roleId: string, updates: any) => Promise<any>;
    deleteRole: (roleId: string) => Promise<any>;
    updateRolePermissions: (
      roleId: string,
      permissions: Record<string, string[]>
    ) => Promise<any>;
  };

  // Notification preferences
  notifications: {
    preferences: NotificationPreferences;
    updatePreferences: (preferences: NotificationPreferences) => Promise<void>;
    resetToDefaults: () => void;
    isLoading: boolean;
    isSaving: boolean;
  };

  // Settings state
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const SettingsContext = createContext<SettingsContextValue | null>(null);

interface SettingsProviderProps {
  children: React.ReactNode;
}

const defaultNotificationPreferences: NotificationPreferences = {
  email: {
    newMessages: true,
    documentUpdates: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
    weeklyDigest: true,
  },
  push: {
    newMessages: true,
    documentUpdates: false,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
  },
  inApp: {
    newMessages: true,
    documentUpdates: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: true,
    systemAlerts: true,
    roleChanges: true,
  },
};

export function SettingsProvider({ children }: SettingsProviderProps) {
  // RBAC hooks
  const rbacData = useRBAC();
  const permissionsData = usePermissions();
  const profileData = useUserProfile();
  const userManagementData = useUserManagement();
  const roleManagementData = useRoleManagement();

  // Local state
  const [activeTab, setActiveTab] = useState("profile");
  const [notificationPreferences, setNotificationPreferences] =
    useState<NotificationPreferences>(defaultNotificationPreferences);
  const [notificationsLoading, setNotificationsLoading] = useState(true);
  const [notificationsSaving, setNotificationsSaving] = useState(false);

  // Load notification preferences
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const saved = localStorage.getItem("notificationPreferences");
        if (saved) {
          setNotificationPreferences(JSON.parse(saved));
        }
      } catch (error) {
        console.error("Failed to load notification preferences:", error);
      } finally {
        setNotificationsLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Update notification preferences
  const updateNotificationPreferences = useCallback(
    async (preferences: NotificationPreferences) => {
      setNotificationsSaving(true);
      try {
        localStorage.setItem(
          "notificationPreferences",
          JSON.stringify(preferences)
        );
        setNotificationPreferences(preferences);
      } catch (error) {
        console.error("Failed to save notification preferences:", error);
        throw error;
      } finally {
        setNotificationsSaving(false);
      }
    },
    []
  );

  // Reset notification preferences
  const resetNotificationPreferences = useCallback(() => {
    setNotificationPreferences(defaultNotificationPreferences);
  }, []);

  // Enhanced permissions with entity-based checks and convenience flags
  const enhancedPermissions = {
    // Include all original permission functions
    ...permissionsData,

    // Entity-based convenience flags
    canManageUsers:
      permissionsData.hasEntityPermission?.("user", "create") ||
      permissionsData.hasEntityPermission?.("user", "update") ||
      permissionsData.hasEntityPermission?.("user", "delete") ||
      permissionsData.hasPermission?.("user:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canManageRoles:
      permissionsData.hasEntityPermission?.("role", "create") ||
      permissionsData.hasEntityPermission?.("role", "update") ||
      permissionsData.hasEntityPermission?.("role", "delete") ||
      permissionsData.hasPermission?.("role:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canManageDocuments:
      permissionsData.hasEntityPermission?.("document", "create") ||
      permissionsData.hasEntityPermission?.("document", "update") ||
      permissionsData.hasEntityPermission?.("document", "delete") ||
      permissionsData.hasPermission?.("document:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canManageProposals:
      permissionsData.hasEntityPermission?.("proposal", "create") ||
      permissionsData.hasEntityPermission?.("proposal", "update") ||
      permissionsData.hasEntityPermission?.("proposal", "delete") ||
      permissionsData.hasPermission?.("proposal:manage") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    canViewReports:
      permissionsData.hasEntityPermission?.("report", "read") ||
      permissionsData.hasPermission?.("report:view") ||
      permissionsData.hasPermission?.("admin:all") ||
      false,

    isAdmin:
      permissionsData.hasPermission?.("admin:all") ||
      permissionsData.hasEntityPermission?.("admin", "all") ||
      false,
  };

  const contextValue: SettingsContextValue = {
    rbac: rbacData,
    permissions: enhancedPermissions,
    profile: profileData,
    userManagement: userManagementData,
    roleManagement: roleManagementData,
    notifications: {
      preferences: notificationPreferences,
      updatePreferences: updateNotificationPreferences,
      resetToDefaults: resetNotificationPreferences,
      isLoading: notificationsLoading,
      isSaving: notificationsSaving,
    },
    activeTab,
    setActiveTab,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
}

// Hook to use settings context
export function useSettings(): SettingsContextValue {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
}
