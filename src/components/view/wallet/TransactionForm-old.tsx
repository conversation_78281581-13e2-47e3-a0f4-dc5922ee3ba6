"use client";

import React, { Fragment, useState, useEffect } from "react";
import { CreditCard, AlertCircle } from "lucide-react";

import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { But<PERSON> } from "@/components/common/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";

import { usePayment } from "@/hooks/usePayment";
import { useBraintree } from "@/hooks/useBrainTree";
import { useContracts } from "@/hooks/useContracts";

// Company merchant account is automatically configured by the Braintree service
export const TransactionForm = () => {
  const { paymentMethods } = usePayment();
  const { currentContract } = useContracts();
  const {
    isReady,
    clientToken,
    isConnected,
    isProcessingTransaction,
    error,
    initialize,
    processTransaction,
    clearBraintreeError,
  } = useBraintree();

  const [amount, setAmount] = useState("");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("");
  const [description, setDescription] = useState("");

  // Initialize Braintree when component mounts
  useEffect(() => {
    if (!isReady && !isConnected) {
      initialize();
    }
  }, [isReady, isConnected, initialize]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!amount || !selectedPaymentMethod) return;

    // Clear any previous errors
    clearBraintreeError();

    // Find the selected payment method details
    const paymentMethod = paymentMethods?.find(
      (method: any) => method.id === selectedPaymentMethod
    );

    if (!paymentMethod) {
      console.error("Selected payment method not found");
      return;
    }

    if (!currentContract) {
      console.error("No contract found");
      return;
    }

    let paymentMethodOnce = "";

    try {
      // Process transaction using Braintree
      await processTransaction({
        amount: parseFloat(amount),
        orderId: currentContract.id,
        payment_method_id: selectedPaymentMethod,
        description:
          description ||
          `Transaction from ${
            paymentMethod.holderName ||
            (paymentMethod as any).holder_name ||
            "User"
          } (${paymentMethod.type}) to Company Account`,
        // merchantAccountId is automatically set to company account by the service
      });

      // Reset form on success
      setAmount("");
      setSelectedPaymentMethod("");
      setDescription("");
    } catch (error) {
      console.error("Transaction failed:", error);
    }
  };

  // Get available payment methods for the dropdown
  const availablePaymentMethods =
    paymentMethods?.filter(
      (method: any) => method.type !== "braintree" // Exclude Braintree methods to avoid circular transactions
    ) || [];

  return (
    <Fragment>
      {/* Show error if any */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/20 border border-red-700 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-400" />
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Show connection status */}
      {!isReady && (
        <div className="mb-4 p-3 bg-yellow-900/20 border border-yellow-700 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-yellow-400" />
            <span className="text-yellow-400 text-sm">
              Initializing Braintree payment system...
            </span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex flex-col space-y-6">
        <div className="space-y-2">
          <Label htmlFor="transaction-amount" className="text-zinc-300">
            Amount ($)
          </Label>
          <Input
            id="transaction-amount"
            type="number"
            step="0.01"
            min="0.01"
            placeholder="0.00"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="payment-method" className="text-zinc-300">
            Payment Method
          </Label>
          <Select
            value={selectedPaymentMethod}
            onValueChange={setSelectedPaymentMethod}
          >
            <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
              <SelectValue placeholder="Select payment method" />
            </SelectTrigger>
            <SelectContent className="bg-zinc-700 border-zinc-600">
              {availablePaymentMethods.length > 0 ? (
                availablePaymentMethods.map((method: any) => (
                  <SelectItem key={method.id} value={method.id}>
                    <div className="flex items-center gap-2">
                      <CreditCard className="w-4 h-4" />
                      <span>
                        {method.holderName ||
                          (method as any).holder_name ||
                          "User"}{" "}
                        -{" "}
                        {method.refNumber ||
                          (method as any).ref_number ||
                          "****"}{" "}
                        - {method.type}
                        {method.isDefault ? " (Default)" : ""}
                      </span>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="no-methods" disabled>
                  No payment methods available
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="transaction-description" className="text-zinc-300">
            Description (Optional)
          </Label>
          <Input
            id="transaction-description"
            type="text"
            placeholder="Transaction description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
          />
        </div>

        <div className="bg-zinc-700 p-3 rounded-lg flex flex-col gap-1">
          <div className="flex items-center gap-2 mb-2">
            <CreditCard className="w-4 h-4 text-zinc-400" />
            <span className="text-zinc-300 text-sm font-medium">Recipient</span>
          </div>
          <p className="text-zinc-400 text-sm font-semibold">
            Underscore International Company Limited
          </p>
          <p className="text-zinc-500 text-xs"><EMAIL></p>
        </div>

        <Button
          type="submit"
          disabled={
            !amount ||
            !selectedPaymentMethod ||
            isProcessingTransaction ||
            !isReady ||
            availablePaymentMethods.length === 0
          }
          className="w-full bg-orange-600 hover:bg-orange-700 text-white"
        >
          {isProcessingTransaction
            ? "Processing Transaction..."
            : "Send Payment via Braintree"}
        </Button>

        {availablePaymentMethods.length === 0 && (
          <p className="text-amber-400 text-sm text-center">
            Please add a payment method to make transactions
          </p>
        )}
      </form>
    </Fragment>
  );
};
