"use client";

/**
 * RBAC Component Wrapper
 *
 * A wrapper component that conditionally renders children based on RBAC permissions.
 * Supports single and multiple permission checks with flexible fallback options.
 */

import React from "react";
import { useRBAC } from "@/lib/rbac/context";
import {
  RBACWrapperProps,
  EntityType,
  PermissionAction,
} from "@/lib/rbac/types";
import AccessDenied from "./AccessDenied";

interface ExtendedRBACWrapperProps extends RBACWrapperProps {
  // Additional props for enhanced functionality
  permissions?: Array<{
    entity: EntityType;
    action: PermissionAction;
    resourceId?: string;
  }>;
  showLoader?: boolean;
  loaderComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  onPermissionDenied?: () => void;
}

/**
 * RBAC Wrapper Component
 *
 * Conditionally renders children based on user permissions.
 *
 * @param entity - The entity type to check permissions for
 * @param action - The action to check permissions for
 * @param resourceId - Optional resource-specific permission check
 * @param permissions - Array of multiple permissions to check (alternative to entity/action)
 * @param requireAll - If true, requires all permissions; if false, requires any permission
 * @param showLoader - Whether to show loader during permission check
 * @param loaderComponent - Custom loader component
 * @param errorComponent - Custom error component
 * @param onPermissionDenied - Callback when permission is denied
 * @param children - Content to render when permission is granted
 */
export function RBACWrapper({
  entity,
  action,
  resourceId,
  permissions,
  requireAll = true,
  showLoader = false,
  loaderComponent,
  errorComponent,
  onPermissionDenied,
  children,
}: ExtendedRBACWrapperProps) {
  const { hasEntityPermission, isLoading, error } = useRBAC();

  // Handle loading state
  if (isLoading && showLoader) {
    return (
      <>
        {loaderComponent || (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
          </div>
        )}
      </>
    );
  }

  // Handle error state
  if (error) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }

    // Default error display (only in development)
    if (process.env.NODE_ENV === "development") {
      return (
        <div className="text-red-500 text-sm p-2 border border-red-200 rounded">
          RBAC Error: {error}
        </div>
      );
    }

    return <AccessDenied />;
  }

  // Check permissions
  let hasRequiredPermissions = false;

  if (permissions && permissions.length > 0) {
    // Multiple permissions check
    const permissionResults = permissions.map((perm) =>
      hasEntityPermission(perm.entity, perm.action)
    );

    hasRequiredPermissions = requireAll
      ? permissionResults.every((result) => result)
      : permissionResults.some((result) => result);
  } else if (entity && action) {
    // Single permission check
    hasRequiredPermissions = hasEntityPermission(entity, action);
  }

  // Handle permission denied
  if (!hasRequiredPermissions) {
    if (onPermissionDenied) {
      onPermissionDenied();
    }
    return <AccessDenied />;
  }

  // Render children when permission is granted
  return <>{children}</>;
}

/**
 * Higher-order component for RBAC protection
 */
export function withRBAC<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  rbacProps: Omit<ExtendedRBACWrapperProps, "children">
) {
  const RBACProtectedComponent = (props: P) => {
    return (
      <RBACWrapper {...rbacProps}>
        <WrappedComponent {...props} />
      </RBACWrapper>
    );
  };

  RBACProtectedComponent.displayName = `withRBAC(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return RBACProtectedComponent;
}

/**
 * Specialized wrapper for admin-only content
 */
export function AdminOnly({ children }: { children: React.ReactNode }) {
  return (
    <RBACWrapper entity="admin" action="read">
      {children}
    </RBACWrapper>
  );
}

/**
 * Specialized wrapper for create operations
 */
export function CanCreate({
  entity,
  children,
  resourceId,
}: {
  entity: EntityType;
  children: React.ReactNode;
  resourceId?: string;
}) {
  return (
    <RBACWrapper entity={entity} action="create" resourceId={resourceId}>
      {children}
    </RBACWrapper>
  );
}

/**
 * Specialized wrapper for read operations
 */
export function CanRead({
  entity,
  children,
  resourceId,
}: {
  entity: EntityType;
  children: React.ReactNode;
  resourceId?: string;
}) {
  return (
    <RBACWrapper entity={entity} action="read" resourceId={resourceId}>
      {children}
    </RBACWrapper>
  );
}

/**
 * Specialized wrapper for update operations
 */
export function CanUpdate({
  entity,
  children,
  resourceId,
}: {
  entity: EntityType;
  children: React.ReactNode;
  resourceId?: string;
}) {
  return (
    <RBACWrapper entity={entity} action="update" resourceId={resourceId}>
      {children}
    </RBACWrapper>
  );
}

/**
 * Specialized wrapper for delete operations
 */
export function CanDelete({
  entity,
  children,
  resourceId,
}: {
  entity: EntityType;
  children: React.ReactNode;
  resourceId?: string;
}) {
  return (
    <RBACWrapper entity={entity} action="delete" resourceId={resourceId}>
      {children}
    </RBACWrapper>
  );
}

/**
 * Conditional wrapper that shows different content based on permissions
 * Note: This function now always shows AccessDenied component when permission is denied.
 * The 'denied' prop is no longer used as we use the centralized AccessDenied component.
 */
export function RBACConditional({
  entity,
  action,
  resourceId,
  permissions,
  requireAll = true,
  granted,
  loading,
}: {
  entity?: EntityType;
  action?: PermissionAction;
  resourceId?: string;
  permissions?: Array<{
    entity: EntityType;
    action: PermissionAction;
    resourceId?: string;
  }>;
  requireAll?: boolean;
  granted: React.ReactNode;
  loading?: React.ReactNode;
}) {
  return (
    <RBACWrapper
      entity={entity!}
      action={action!}
      resourceId={resourceId}
      permissions={permissions}
      requireAll={requireAll}
      showLoader={!!loading}
      loaderComponent={loading}
    >
      {granted}
    </RBACWrapper>
  );
}
