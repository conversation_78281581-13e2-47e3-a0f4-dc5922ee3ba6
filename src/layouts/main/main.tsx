"use client";

import { Providers } from "@/providers";
import { Toaster } from "@/components/common/ui/sonner";

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>
        <Providers>
          {/* Interferes with the Dashboard view */}
          {/* <Toaster position="top-center" /> */}
          {children}
        </Providers>
      </body>
    </html>
  );
}
