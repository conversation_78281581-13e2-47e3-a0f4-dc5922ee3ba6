import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import { Checkbox } from "@/components/common/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { PaymentMethod } from "./types";

interface AddPaymentMethodFormProps {
  mode?: "create" | "edit";
  editingMethod?: PaymentMethod | null;
  onClose: () => void;
  onAdd: (method: Omit<PaymentMethod, "id" | "createdAt">) => void;
  onUpdate?: (id: string, method: Partial<PaymentMethod>) => void;
}

export const AddPaymentMethodForm: React.FC<AddPaymentMethodFormProps> = ({
  mode = "create",
  editingMethod = null,
  onClose,
  onAdd,
  onUpdate,
}) => {
  // Initialize form data based on mode
  const getInitialFormData = () => {
    if (mode === "edit" && editingMethod) {
      return {
        type: editingMethod.type,
        provider: editingMethod.provider,
        last4: editingMethod.last4 || "",
        expiryMonth: editingMethod.expiryMonth || "",
        expiryYear: editingMethod.expiryYear || "",
        cvv: editingMethod.cvv || "",
        holderName: editingMethod.holderName || "",
        email: editingMethod.email || "",
        bankName: editingMethod.bankName || "",
        accountType:
          (editingMethod.accountType as "checking" | "savings") || "checking",
      };
    }

    return {
      type: "credit_card" as PaymentMethod["type"],
      provider: "visa" as PaymentMethod["provider"],
      last4: "",
      expiryMonth: "",
      expiryYear: "",
      cvv: "",
      holderName: "",
      email: "",
      bankName: "",
      accountType: "checking" as "checking" | "savings",
      isDefault: false,
    };
  };

  const [formData, setFormData] = useState(getInitialFormData());

  // Update form data when editingMethod changes
  useEffect(() => {
    if (mode === "edit" && editingMethod) {
      setFormData({
        type: editingMethod.type,
        provider: editingMethod.provider,
        last4: editingMethod.last4 || "",
        expiryMonth: editingMethod.expiryMonth || "",
        expiryYear: editingMethod.expiryYear || "",
        cvv: editingMethod.cvv || "",
        holderName: editingMethod.holderName || "",
        email: editingMethod.email || "",
        bankName: editingMethod.bankName || "",
        accountType:
          (editingMethod.accountType as "checking" | "savings") || "checking",
        isDefault: editingMethod.isDefault || false,
      });
    } else if (mode === "create") {
      setFormData({
        type: "credit_card" as PaymentMethod["type"],
        provider: "visa" as PaymentMethod["provider"],
        last4: "",
        expiryMonth: "",
        expiryYear: "",
        cvv: "",
        holderName: "",
        email: "",
        bankName: "",
        accountType: "checking" as "checking" | "savings",
        isDefault: false,
      });
    }
  }, [mode, editingMethod]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const methodData = {
      type: formData.type,
      provider: formData.provider,
      holderName: formData.holderName,
      cvv: formData.cvv,
      ...(formData.type === "credit_card" || formData.type === "debit_card"
        ? {
            last4: formData.last4,
            expiryMonth: formData.expiryMonth,
            expiryYear: formData.expiryYear,
          }
        : {}),
      ...(formData.type === "paypal"
        ? {
            email: formData.email,
          }
        : {}),
      ...(formData.type === "bank_account"
        ? {
            bankName: formData.bankName,
            accountType: formData.accountType,
            last4: formData.last4,
          }
        : {}),
    };

    if (mode === "edit" && editingMethod && onUpdate) {
      // Update existing payment method
      onUpdate(editingMethod.id, methodData);
    } else {
      // Create new payment method
      const newMethod: Omit<PaymentMethod, "id" | "createdAt"> = {
        ...methodData,
        isDefault: false,
        isVerified: false,
      };
      onAdd(newMethod);
    }

    onClose();
  };

  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-white">
          {mode === "edit" ? "Edit Payment Method" : "Add Payment Method"}
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-zinc-400 hover:text-white"
        >
          <X className="w-4 h-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label className="text-zinc-300">Payment Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value) =>
                setFormData({
                  ...formData,
                  type: value as PaymentMethod["type"],
                })
              }
            >
              <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-zinc-700 border-zinc-600">
                <SelectItem value="credit_card">Credit Card</SelectItem>
                <SelectItem value="debit_card">Debit Card</SelectItem>
                <SelectItem value="paypal">PayPal</SelectItem>
                <SelectItem value="bank_account">Bank Account</SelectItem>
                <SelectItem value="apple_pay">Apple Pay</SelectItem>
                <SelectItem value="google_pay">Google Pay</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {(formData.type === "credit_card" ||
            formData.type === "debit_card") && (
            <>
              <div className="space-y-2">
                <Label className="text-zinc-300">Card Provider</Label>
                <Select
                  value={formData.provider}
                  onValueChange={(value) =>
                    setFormData({
                      ...formData,
                      provider: value as PaymentMethod["provider"],
                    })
                  }
                >
                  <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-700 border-zinc-600">
                    <SelectItem value="visa">Visa</SelectItem>
                    <SelectItem value="mastercard">Mastercard</SelectItem>
                    <SelectItem value="amex">American Express</SelectItem>
                    <SelectItem value="discover">Discover</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-zinc-300">Card Number</Label>
                  <Input
                    type="text"
                    placeholder="1234 1234 1234 1234"
                    maxLength={12}
                    value={formData.last4}
                    onChange={(e) =>
                      setFormData({ ...formData, last4: e.target.value })
                    }
                    className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-zinc-300">Cardholder Name</Label>
                  <Input
                    type="text"
                    placeholder="John Doe"
                    value={formData.holderName}
                    onChange={(e) =>
                      setFormData({ ...formData, holderName: e.target.value })
                    }
                    className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label className="text-zinc-300">Expiry Month</Label>
                  <Select
                    value={formData.expiryMonth}
                    onValueChange={(value) =>
                      setFormData({ ...formData, expiryMonth: value })
                    }
                  >
                    <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
                      <SelectValue placeholder="Month" />
                    </SelectTrigger>
                    <SelectContent className="bg-zinc-700 border-zinc-600">
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem
                          key={i + 1}
                          value={String(i + 1).padStart(2, "0")}
                        >
                          {String(i + 1).padStart(2, "0")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-zinc-300">Expiry Year</Label>
                  <Select
                    value={formData.expiryYear}
                    onValueChange={(value) =>
                      setFormData({ ...formData, expiryYear: value })
                    }
                  >
                    <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent className="bg-zinc-700 border-zinc-600">
                      {Array.from({ length: 10 }, (_, i) => (
                        <SelectItem key={2024 + i} value={String(2024 + i)}>
                          {2024 + i}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-zinc-300">CVV</Label>
                  <Input
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={4}
                    minLength={3}
                    placeholder="123"
                    value={formData.cvv}
                    onChange={(e) => {
                      // Only allow numeric input and limit to 4 characters
                      const value = e.target.value
                        .replace(/\D/g, "")
                        .slice(0, 4);
                      setFormData({ ...formData, cvv: value });
                    }}
                    className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                  />
                </div>
              </div>
            </>
          )}

          {formData.type === "paypal" && (
            <>
              <div className="space-y-2">
                <Label className="text-zinc-300">PayPal Email</Label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-zinc-300">Account Holder Name</Label>
                <Input
                  type="text"
                  placeholder="John Doe"
                  value={formData.holderName}
                  onChange={(e) =>
                    setFormData({ ...formData, holderName: e.target.value })
                  }
                  className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                />
              </div>
            </>
          )}

          {formData.type === "bank_account" && (
            <>
              <div className="space-y-2">
                <Label className="text-zinc-300">Bank Name</Label>
                <Input
                  type="text"
                  placeholder="Chase Bank"
                  value={formData.bankName}
                  onChange={(e) =>
                    setFormData({ ...formData, bankName: e.target.value })
                  }
                  className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-zinc-300">Account Type</Label>
                  <Select
                    value={formData.accountType}
                    onValueChange={(value) =>
                      setFormData({
                        ...formData,
                        accountType: value as "checking" | "savings",
                      })
                    }
                  >
                    <SelectTrigger className="bg-zinc-700 border-zinc-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-zinc-700 border-zinc-600">
                      <SelectItem value="checking">Checking</SelectItem>
                      <SelectItem value="savings">Savings</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-zinc-300">Last 4 Digits</Label>
                  <Input
                    type="text"
                    placeholder="1234"
                    maxLength={4}
                    value={formData.last4}
                    onChange={(e) =>
                      setFormData({ ...formData, last4: e.target.value })
                    }
                    className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-zinc-300">Account Holder Name</Label>
                <Input
                  type="text"
                  placeholder="John Doe"
                  value={formData.holderName}
                  onChange={(e) =>
                    setFormData({ ...formData, holderName: e.target.value })
                  }
                  className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
                />
              </div>
            </>
          )}

          {(formData.type === "apple_pay" ||
            formData.type === "google_pay") && (
            <div className="space-y-2">
              <Label className="text-zinc-300">Account Name</Label>
              <Input
                type="text"
                placeholder="John Doe"
                value={formData.holderName}
                onChange={(e) =>
                  setFormData({ ...formData, holderName: e.target.value })
                }
                className="bg-zinc-700 border-zinc-600 text-white placeholder:text-zinc-400"
              />
            </div>
          )}

          {/* Default Payment Method Checkbox */}
          <div className="flex items-center space-x-2 pt-2">
            <Checkbox
              id="default"
              checked={formData.isDefault}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isDefault: !!checked })
              }
            />
            <Label
              htmlFor="default"
              className="text-zinc-300 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Set as default payment method
            </Label>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              className="flex-1 bg-lime-600 hover:bg-lime-700 text-white"
            >
              {mode === "edit" ? "Update Payment Method" : "Add Payment Method"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 border-zinc-600 text-zinc-300 hover:bg-zinc-700"
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
