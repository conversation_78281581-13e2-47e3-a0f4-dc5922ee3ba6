"use client";

import React, { useState } from "react";

import { Badge } from "@/components/common/ui/badge";
import { Button } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { useAuth } from "@/hooks/useAuth";
import {
  Search,
  Plus,
  MessageCircle,
  Users,
  Hash,
  MoreHorizontal,
  Edit,
  Trash2,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

interface Room {
  id: string;
  name: string;
  about?: string;
  accountId?: string; // Room owner/creator account ID
  members: Array<{
    id: string;
    accountId: string;
    state: "online" | "offline" | "typing" | "away";
  }>;
  lastMessage?: {
    id: string;
    content: string;
    createdAt: Date;
    sender?: {
      name: string;
    };
  };
  unreadCount: number;
  updatedAt: Date;
}

interface RoomListProps {
  rooms: Room[];
  currentRoomId?: string | null;
  onSelectRoom: (roomId: string) => void;
  onCreateRoom?: () => void;
  onEditRoom?: (room: Room) => void;
  onDeleteRoom?: (room: Room) => void;
  isLoading?: boolean;
  className?: string;
}

interface RoomItemProps {
  room: Room;
  isSelected: boolean;
  onClick: () => void;
  onEdit?: (room: Room) => void;
  onDelete?: (room: Room) => void;
}

function RoomItem({
  room,
  isSelected,
  onClick,
  onEdit,
  onDelete,
}: RoomItemProps) {
  const { permissions, isCurrentSessionYours } = useAuth();

  const onlineMembers = room.members.filter((m) => m.state === "online").length;
  const lastMessageText =
    room.lastMessage?.content
      ?.replace(/<[^>]*>/g, "") // Strip HTML
      ?.trim()
      ?.slice(0, 50) +
    (room.lastMessage?.content && room.lastMessage.content.length > 50
      ? "..."
      : "");

  // Check if current user is the room owner
  const isRoomOwner: boolean = isCurrentSessionYours(room?.accountId);

  const handleDropdownClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent room selection when clicking dropdown
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(room);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(room);
  };

  return (
    <div
      className={cn(
        "flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors group",
        isSelected && "bg-lime-450 border-l-4 border-l-lime-300"
      )}
    >
      <div onClick={onClick} className="flex items-center gap-3 flex-1 min-w-0">
        {/* Room avatar/icon */}
        <div className="flex-shrink-0">
          <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
            <Hash className="h-5 w-5 text-gray-600" />
          </div>
        </div>

        {/* Room info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <p className="font-semibold truncate">{room.name}</p>
            {room.lastMessage && (
              <small className="text-xs text-gray-500 flex-shrink-0">
                {formatDistanceToNow(new Date(room.lastMessage.createdAt), {
                  addSuffix: false,
                })}
              </small>
            )}
          </div>

          {/* Last message or description */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500 truncate">
              {room.lastMessage ? (
                <>
                  <span className="font-medium">
                    {room.lastMessage.sender?.name || "Someone"}:
                  </span>{" "}
                  {lastMessageText}
                </>
              ) : (
                room.about || "No messages yet"
              )}
            </p>

            <div className="flex items-center gap-1 flex-shrink-0 ml-2">
              {/* Unread count */}
              {room.unreadCount > 0 && (
                <Badge
                  variant="destructive"
                  className="h-5 min-w-[20px] text-xs px-1"
                >
                  {room.unreadCount > 99 ? "99+" : room.unreadCount}
                </Badge>
              )}

              {/* Online members indicator */}
              {onlineMembers > 0 && (
                <div className="flex items-center gap-1">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-gray-500">{onlineMembers}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Dropdown Menu */}
      {(onEdit || onDelete) && (
        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={handleDropdownClick}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onEdit &&
                (isRoomOwner ||
                  (permissions as any)?.room?.includes("update")) && (
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
              {onDelete &&
                (isRoomOwner ||
                  (permissions as any)?.room?.includes("delete")) && (
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </div>
  );
}

export function RoomList({
  rooms,
  currentRoomId,
  onSelectRoom,
  onCreateRoom,
  onEditRoom,
  onDeleteRoom,
  isLoading = false,
  className,
}: RoomListProps) {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter rooms based on search query
  const filteredRooms = rooms.filter(
    (room) =>
      room.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      room.about?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-lg font-semibold">Chat</h4>
          {onCreateRoom && (
            <Button onClick={onCreateRoom} size="sm" className="h-8 w-8 p-0">
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {/* Room list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">Loading conversations...</p>
          </div>
        ) : filteredRooms.length === 0 ? (
          <div className="p-4 text-center">
            {searchQuery ? (
              <div className="flex flex-col items-center">
                <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No conversations found</p>
                <p className="text-xs text-gray-400">
                  Try a different search term
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No conversations yet</p>
                {onCreateRoom && (
                  <Button
                    onClick={onCreateRoom}
                    variant="outline"
                    size="sm"
                    className="mt-2"
                  >
                    Start a conversation
                  </Button>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredRooms.map((room) => (
              <RoomItem
                key={room.id}
                room={room}
                isSelected={room.id === currentRoomId}
                onClick={() => onSelectRoom(room.id)}
                onEdit={onEditRoom}
                onDelete={onDeleteRoom}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer with stats */}
      <div className="p-3 border-t border-zinc-800 flex-shrink-0">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {rooms.length} conversation{rooms.length !== 1 ? "s" : ""}
          </span>
          <div className="flex items-center gap-2">
            <Users className="h-3 w-3" />
            <span>
              {rooms.reduce(
                (acc, room) =>
                  acc + room.members.filter((m) => m.state === "online").length,
                0
              )}{" "}
              online
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
