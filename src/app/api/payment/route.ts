"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { PaymentMethodService } from "@/lib/api/services/payment";

// GET /api/payment - Get all payment methods for authenticated user
export async function GET(request: NextRequest) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Check for query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");
    const search = searchParams.get("search");
    const stats = searchParams.get("stats");

    let result;

    // Handle different query types
    if (stats === "true") {
      // Get payment method statistics
      result = await paymentService.getPaymentMethodStatistics();
    } else if (type) {
      // Get payment methods by type
      result = await paymentService.getPaymentMethodsByType(type);
    } else if (search) {
      // Search payment methods
      result = await paymentService.searchPaymentMethods(search);
    } else {
      // Get all payment methods
      result = await paymentService.getPaymentMethods();
    }

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment methods GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/payment - Create new payment method
export async function POST(request: NextRequest) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const paymentMethodData = await request.json();

    // Check if this is a validation request
    if (paymentMethodData.validate === true) {
      // Validate payment method data without saving
      const result = await paymentService.validatePaymentMethodData(
        paymentMethodData
      );
      return NextResponse.json(result, {
        status: result.success ? 200 : result.statusCode || 400,
      });
    }

    // Use the service to create payment method
    const result = await paymentService.createPaymentMethod(paymentMethodData);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment method POST error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/payment - Bulk delete payment methods
export async function DELETE(request: NextRequest) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body to get IDs
    const { ids } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Payment method IDs are required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to bulk delete payment methods
    const result = await paymentService.bulkDeletePaymentMethods(ids);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment methods bulk DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
