import { BaseService } from "./base";
import { prisma } from "@/lib/common/prisma";
import {
  CreateRoomSchema,
  UpdateRoomSchema,
  RoomQuerySchema,
  type CreateRoom,
  type UpdateRoom,
  type RoomQuery,
} from "../validators/schemas/room";

export class RoomService extends BaseService {
  private requiredAuth: boolean;

  constructor({ requiredAuth = true }: { requiredAuth?: boolean } = {}) {
    super();
    this.setModel(prisma.room as any);
    this.requiredAuth = requiredAuth;
  }

  /**
   * Get all rooms with optional filtering and pagination
   */
  async getRooms(queryParams: Partial<RoomQuery> = {}): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Fetching rooms", { queryParams });

      // Validate query parameters
      const validatedQuery = RoomQuerySchema.parse(queryParams);
      const {
        page,
        limit,
        search,
        contractId,
        accountId,
        includeMembers,
        includeMessages,
        includeContract,
      } = validatedQuery;

      // Build where clause
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { about: { contains: search, mode: "insensitive" } },
        ];
      }

      if (contractId) {
        where.contractId = contractId;
      }

      if (accountId) {
        where.accountId = accountId;
      }

      // Build include clause
      const include: any = {
        account: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            members: true,
            messages: true,
          },
        },
      };

      if (includeContract) {
        include.contract = {
          select: {
            id: true,
            name: true,
          },
        };
      }

      if (includeMembers) {
        include.members = {
          include: {
            account: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
          },
        };
      }

      if (includeMessages) {
        include.messages = {
          take: 10,
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            content: true,
            createdAt: true,
          },
        };
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const total = await this.countRecords({ where });

      // Fetch rooms
      const rooms = await this.findManyRecords({
        where,
        include,
        orderBy: {
          updatedAt: "desc",
        },
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      this.log("info", `Found ${rooms.length} rooms`);

      return {
        rooms,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    }, "getRooms");
  }

  /**
   * Get a single room by ID
   */
  async getRoomById(id: string, includeRelations = true): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Fetching room by ID: ${id}`);

      if (!id) {
        throw new Error("Room ID is required");
      }

      const include = includeRelations
        ? {
            account: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
            contract: {
              select: {
                id: true,
                name: true,
              },
            },
            members: {
              include: {
                account: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                      },
                    },
                  },
                },
              },
            },
            messages: {
              take: 50,
              orderBy: {
                createdAt: "desc",
              },
              include: {
                attachments: {
                  select: {
                    id: true,
                    name: true,
                    path: true,
                    file_type: true,
                    size: true,
                  },
                },
              },
            },
            _count: {
              select: {
                members: true,
                messages: true,
              },
            },
          }
        : undefined;

      const room = await this.findUniqueRecord({
        where: { id },
        include,
      });

      if (!room) {
        throw new Error("Room not found");
      }

      this.log("info", `Room found: ${(room as any).name}`);

      return room;
    }, "getRoomById");
  }

  /**
   * Create a new room
   */
  async createRoom(roomData: CreateRoom): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", "Creating room", { name: roomData.name });

      // Validate input
      const validatedData = CreateRoomSchema.parse(roomData);

      // If no accountId provided, get current user's account
      if (!validatedData.accountId) {
        const { id: accountId } = await this.getCurrentUserAccount();
        validatedData.accountId = accountId;
      }

      let { members, ...roomDataWithoutMembers } = validatedData;

      // Create room
      const room = await this.createRecord({
        data: roomDataWithoutMembers,
        include: {
          account: {
            select: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          contract: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!room) {
        throw new Error("Failed to create room");
      }

      // Add provided members
      if (validatedData.members) {
        this.setModel(prisma.member as any);

        // Get account IDs for provided members
        const transformedMembers = await Promise.all(
          validatedData.members.map(async (member: any) => {
            let account = await this.getUserAccount(member);
            return account.id;
          })
        );

        await this.addMembers((room as any).id, transformedMembers);
      }

      // If contractId provided, add client as a member
      if (validatedData.contractId) {
        // Get client ID from contract
        this.setModel(prisma.contract as any);
        let client: any = await this.findUniqueRecord({
          where: { id: validatedData.contractId },
          select: { clientId: true },
        }).then(async (contract: any) => {
          return await this.getUserAccount(contract.clientId);
        });

        await this.addMember((room as any).id, client.id);
      }

      // Add owner as a member
      if (validatedData.accountId) {
        await this.addMember((room as any).id, validatedData.accountId);
      }

      this.log("info", `Room created: ${(room as any).name}`);

      return room;
    }, "createRoom");
  }

  /**
   * Update an existing room
   */
  async updateRoom(id: string, roomData: UpdateRoom): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Updating room: ${id}`, { data: roomData });

      // Validate input
      const validatedData = UpdateRoomSchema.parse({ ...roomData, id });

      // Check if room exists
      const existingRoom = await this.findUniqueRecord({
        where: { id },
        select: { id: true, accountId: true },
      });

      if (!existingRoom) {
        throw new Error("Room not found");
      }

      // Check if user has permission to update (room owner or admin)
      const { id: currentAccountId } = await this.getCurrentUserAccount();
      if ((existingRoom as any).accountId !== currentAccountId) {
        // TODO: Add admin role check here
        throw new Error(
          "Permission denied: You can only update rooms you created"
        );
      }

      // Update room
      const { id: _, ...updateData } = validatedData;
      const updatedRoom = await this.updateRecord({
        where: { id },
        data: updateData,
        include: {
          account: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
            },
          },
          contract: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              members: true,
              messages: true,
            },
          },
        },
      });

      this.log("info", `Room updated: ${(updatedRoom as any).name}`);

      return updatedRoom;
    }, "updateRoom");
  }

  /**
   * Delete a room
   */
  async deleteRoom(id: string): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Deleting room: ${id}`);

      if (!id) {
        throw new Error("Room ID is required");
      }

      // Check if room exists
      const existingRoom = await this.findUniqueRecord({
        where: { id },
        select: { id: true, accountId: true, name: true },
      });

      if (!existingRoom) {
        throw new Error("Room not found");
      }

      // Check if user has permission to delete (room owner or admin)
      const { id: currentAccountId } = await this.getCurrentUserAccount();
      if ((existingRoom as any).accountId !== currentAccountId) {
        // TODO: Add admin role check here
        throw new Error(
          "Permission denied: You can only delete rooms you created"
        );
      }

      const originalModel = this.model || (prisma.room as any);

      try {
        // Delete all members first
        this.setModel(prisma.member as any);
        await this.deleteManyRecords({
          where: { roomId: id },
        });

        // Delete all messages
        this.setModel(prisma.message as any);
        await this.deleteManyRecords({
          where: { roomId: id },
        });

        // Delete the room
        this.setModel(prisma.room as any);
        await this.deleteRecord({
          where: { id },
        });
      } finally {
        this.setModel(originalModel);
      }

      this.log("info", `Room deleted: ${(existingRoom as any).name}`);

      return { id, message: "Room deleted successfully" };
    }, "deleteRoom");
  }

  /**
   * Add a single member to a room (uses bulk operation internally)
   */
  async addMember(roomId: string, accountId: string): Promise<any> {
    const result = await this.addMembers(roomId, [accountId]);

    if (result.existing && result.existing.includes(accountId)) {
      throw new Error("User is already a member of this room");
    }

    return result.added && result.added.length > 0 ? result.added[0] : null;
  }

  /**
   * Add a member to a room
   */
  async addMembers(roomId: string, accountIds: string[]): Promise<any> {
    return this.executeOperation(async () => {
      this.log(
        "info",
        `Adding ${accountIds.length} members to room: ${roomId}`,
        { accountIds }
      );

      if (!accountIds || accountIds.length === 0) {
        throw new Error("No account IDs provided");
      }

      const originalModel: any = prisma.room as any;

      this.setModel(originalModel);

      try {
        // Check if room exists
        const room = await this.findUniqueRecord({
          where: { id: roomId },
          select: { id: true, name: true },
        });

        if (!room) {
          throw new Error("Room not found");
        }

        this.setModel(prisma.member as any);

        // Bulk check for existing members using 'in' filter
        const existingMembers = await this.findManyRecords({
          where: { roomId, accountId: { in: accountIds } },
          select: { accountId: true },
        });

        // Extract existing account IDs
        const existingAccountIds = existingMembers.map(
          (member: any) => member.accountId
        );

        // Filter out accounts that are already members
        const newAccountIds = accountIds.filter(
          (accountId) => !existingAccountIds.includes(accountId)
        );

        if (newAccountIds.length === 0) {
          this.log(
            "info",
            "All provided accounts are already members of this room"
          );
          return {
            added: [],
            existing: existingAccountIds,
            message: "All users are already members of this room",
          };
        }

        // Prepare bulk data for creation
        const memberData = newAccountIds.map((accountId) => ({
          roomId,
          accountId,
        }));

        // Bulk create new members
        await this.createManyRecords({
          data: memberData,
        });

        // Fetch the created members with their account details
        const createdMembers = await this.findManyRecords({
          where: {
            roomId,
            accountId: {
              in: newAccountIds,
            },
          },
          select: {
            account: {
              select: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
          },
        });

        this.log(
          "info",
          `Successfully added ${newAccountIds.length} members to room: ${
            (room as any).name
          }`
        );

        return {
          added: createdMembers,
          existing: existingAccountIds,
          count: newAccountIds.length,
          message: `Successfully added ${newAccountIds.length} member(s) to the room`,
        };
      } finally {
        this.setModel(originalModel);
      }
    }, "addMembers");
  }

  /**
   * Remove multiple members from a room (bulk operation)
   */
  async removeMembers(roomId: string, accountIds: string[]): Promise<any> {
    return this.executeOperation(async () => {
      this.log(
        "info",
        `Removing ${accountIds.length} members from room: ${roomId}`,
        { accountIds }
      );

      if (!accountIds || accountIds.length === 0) {
        throw new Error("No account IDs provided");
      }

      const originalModel = this.model || (prisma.room as any);

      try {
        // Check if room exists
        const room = await this.findUniqueRecord({
          where: { id: roomId },
          select: { id: true, name: true, accountId: true },
        });

        if (!room) {
          throw new Error("Room not found");
        }

        // Check permissions (room owner or the member themselves)
        const { id: currentAccountId } = await this.getCurrentUserAccount();
        if (
          (room as any).accountId !== currentAccountId &&
          !accountIds.includes(currentAccountId)
        ) {
          // Allow if user is removing themselves or if they're the room owner
          const isRemovingSelf =
            accountIds.length === 1 && accountIds[0] === currentAccountId;
          if (!isRemovingSelf) {
            throw new Error("Permission denied");
          }
        }

        this.setModel(prisma.member as any);

        // Bulk check which members actually exist using 'in' filter
        const existingMembers = await this.findManyRecords({
          where: {
            roomId,
            accountId: {
              in: accountIds,
            },
          },
          select: {
            accountId: true,
          },
        });

        const existingAccountIds = existingMembers.map(
          (member: any) => member.accountId
        );
        const nonExistingAccountIds = accountIds.filter(
          (accountId) => !existingAccountIds.includes(accountId)
        );

        if (existingAccountIds.length === 0) {
          this.log(
            "info",
            "None of the provided accounts are members of this room"
          );
          return {
            removed: [],
            notFound: nonExistingAccountIds,
            message: "None of the specified users were members of this room",
          };
        }

        // Bulk remove members using 'in' filter
        await this.deleteManyRecords({
          where: {
            roomId,
            accountId: {
              in: existingAccountIds,
            },
          },
        });

        this.log(
          "info",
          `Successfully removed ${
            existingAccountIds.length
          } members from room: ${(room as any).name}`
        );

        return {
          removed: existingAccountIds,
          notFound: nonExistingAccountIds,
          count: existingAccountIds.length,
          message: `Successfully removed ${existingAccountIds.length} member(s) from the room`,
        };
      } finally {
        this.setModel(originalModel);
      }
    }, "removeMembers");
  }

  /**
   * Remove a single member from a room
   */
  async removeMember(roomId: string, accountId: string): Promise<any> {
    return this.executeOperation(async () => {
      this.log("info", `Removing member from room: ${roomId}`, { accountId });

      const originalModel = this.model || (prisma.room as any);

      try {
        // Check if room exists
        const room = await this.findUniqueRecord({
          where: { id: roomId },
          select: { id: true, name: true, accountId: true },
        });

        if (!room) {
          throw new Error("Room not found");
        }

        // Check permissions (room owner or the member themselves)
        const { id: currentAccountId } = await this.getCurrentUserAccount();
        if (
          (room as any).accountId !== currentAccountId &&
          currentAccountId !== accountId
        ) {
          throw new Error("Permission denied");
        }

        // Remove member
        this.setModel(prisma.member as any);
        await this.deleteRecord({
          where: {
            roomId_accountId: {
              roomId,
              accountId,
            },
          },
        });

        this.log("info", `Member removed from room: ${(room as any).name}`);

        return { message: "Member removed successfully" };
      } finally {
        this.setModel(originalModel);
      }
    }, "removeMember");
  }
}
