import { redirect } from "next/navigation";
import { api } from "@/lib/common/requests";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { signIn, signOut, getSession } from "next-auth/react";
import { setSession, setLoading } from "../slices/auth";
import { toast } from "sonner";
import { fetchSignedURL } from "./documents";

// Async thunk for refreshing session
export const refreshSession = createAsyncThunk(
  "auth/refreshSession",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const session = await getSession();
      dispatch(setSession(session));
      return session;
    } catch (error) {
      console.error("Failed to refresh session:", error);
      dispatch(setSession(null));
      const errorMessage =
        error instanceof Error ? error.message : "Failed to refresh session";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const registerUserWithCredentials = createAsyncThunk(
  "auth/registerUser",
  async (
    credentials: { name: string; email: string; password: string },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const result = await api.post("auth/register", {
        ...credentials,
      });

      if (result.success && !result.error) {
        toast.success(
          result.message || "Registration successful! Please sign in."
        );
        // Redirect will happen after the action completes
        setTimeout(() => redirect("/signin"), 100);
        return result;
      } else {
        toast.error(result.message || "Registration failed");
        return rejectWithValue(result);
      }
    } catch (error) {
      console.error("Registration failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Registration failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const updateUserAvatar = createAsyncThunk(
  "auth/updateUserProfile",
  async (
    { id, avatar }: { id: string; avatar: FormData },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.upload("auth/avatar?id=" + id, avatar);
      if (result.success) {
        toast.success("Profile updated successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to update profile");
      return rejectWithValue(result.message || "Failed to update profile");
    } catch (error: any) {}
  }
);

// Async thunk for login
export const loginUserWithCredentials = createAsyncThunk(
  "auth/loginUser",
  async (
    credentials: { email: string; password: string },
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setLoading(true));
      const result = await signIn("credentials", {
        ...credentials,
        redirect: false,
      });

      if (!result) {
        toast.error("Login failed");
        return rejectWithValue({
          success: false,
          error: true,
          message: "Login failed",
        });
      }

      if (result.error) {
        toast.error(result.error || "Login failed");
        return rejectWithValue({
          success: false,
          error: true,
          message: result.error || "Login failed",
        });
      }

      // Refresh session after successful login
      const session = await getSession();
      dispatch(setSession(session));
      toast.success("Login successful!");

      return session;
    } catch (error) {
      console.error("Login failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Login failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Async thunk for OAuth login
export const loginUserWithOAuth = createAsyncThunk(
  "auth/loginUserOAuth",
  async (provider: string = "google", { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const result = await signIn(provider, { redirect: false });

      if (!result) {
        toast.error("OAuth login failed");
        return rejectWithValue({
          success: false,
          error: true,
          message: "OAuth login failed",
        });
      }

      if (result.error) {
        toast.error(`${provider} login failed`);
        return rejectWithValue({
          success: false,
          error: true,
          message: result.error,
        });
      }

      // Refresh session after successful login
      const session = await getSession();
      dispatch(setSession(session));
      toast.success(`Successfully signed in with ${provider}!`);

      return session;
    } catch (error) {
      console.error("OAuth login failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "OAuth login failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);

// Async thunk for logout
export const logoutUser = createAsyncThunk(
  "auth/logoutUser",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      await signOut({ redirect: false });
      dispatch(setSession(null));
      toast.success("Successfully signed out!");
      return {
        success: true,
        error: false,
        message: "Successfully signed out!",
      };
    } catch (error) {
      console.error("Logout failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Logout failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    } finally {
      dispatch(setLoading(false));
    }
  }
);
