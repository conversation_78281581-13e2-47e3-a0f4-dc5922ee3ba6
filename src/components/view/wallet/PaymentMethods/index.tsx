"use client";

import React, { useState, useEffect } from "react";
import { usePayment } from "@/hooks/usePayment";
import { CreditCard, Plus } from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { PaymentMethod } from "./types";
import { AddPaymentMethodForm } from "./AddPaymentMethodForm";
import { PaymentMethodCard } from "./PaymentMethodCard";
import { SecuritySection } from "./SecuritySection";
import { mapPaymentType } from "./utils";

// Main Payment Methods Component
export const PaymentMethods = () => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(
    null
  );

  // Use the payment hook for all data and operations
  const {
    paymentMethods,
    error,
    createPaymentMethod,
    updatePaymentMethod,
    setDefaultPaymentMethod,
    deletePaymentMethod,
    clearError,
  } = usePayment();

  // Handle adding new payment method
  const handleAddPaymentMethod = async (
    newMethod: Omit<
      PaymentMethod,
      "id" | "createdAt" | "updatedAt" | "accountId"
    >
  ) => {
    try {
      const result = await createPaymentMethod({
        holder_name: newMethod.holderName,
        ref_number: newMethod.last4 || "",
        expiry_month: newMethod.expiryMonth || "",
        expiry_year: newMethod.expiryYear || "",
        cvv: newMethod.cvv || "", // Use actual CVV from form data
        type: mapPaymentType(newMethod.type),
        isDefault: newMethod.isDefault || false,
      });

      if (
        (result as any).success ||
        (result as any).type?.endsWith("/fulfilled")
      ) {
        setShowAddForm(false);
      }
    } catch (error) {
      console.error("Failed to add payment method:", error);
    }
  };

  // Handle setting default payment method
  const handleSetDefault = async (id: string) => {
    try {
      const result = await setDefaultPaymentMethod(id);
      if (
        (result as any).success ||
        (result as any).type?.endsWith("/fulfilled")
      ) {
        // Success is handled by the hook with toast notifications
      }
    } catch (error) {
      console.error("Failed to set default payment method:", error);
    }
  };

  // Handle deleting payment method
  const handleDelete = async (id: string) => {
    try {
      const result = await deletePaymentMethod(id);
      if (
        !(result as any).success &&
        !(result as any).type?.endsWith("/fulfilled")
      ) {
        console.error(
          "Failed to delete payment method:",
          (result as any).error
        );
      }
    } catch (error) {
      console.error("Failed to delete payment method:", error);
    }
  };

  // Handle editing payment method
  const handleEdit = (id: string) => {
    const method = paymentMethods.find((m: PaymentMethod) => m.id === id);
    if (method) {
      setEditingMethod(method);
      setShowAddForm(true); // Show the form in edit mode
    }
  };

  // Handle updating payment method
  const handleUpdatePaymentMethod = async (
    id: string,
    updatedMethod: Partial<PaymentMethod>
  ) => {
    try {
      const result = await updatePaymentMethod(id, {
        holder_name: updatedMethod.holderName,
        ref_number: updatedMethod.last4,
        expiry_month: updatedMethod.expiryMonth,
        expiry_year: updatedMethod.expiryYear,
        cvv: updatedMethod.cvv || "",
        type: updatedMethod.type
          ? mapPaymentType(updatedMethod.type)
          : undefined,
        isDefault: updatedMethod.isDefault,
      });

      if (
        (result as any).success ||
        (result as any).type?.endsWith("/fulfilled")
      ) {
        setShowAddForm(false);
        setEditingMethod(null);
      }
    } catch (error) {
      console.error("Failed to update payment method:", error);
    }
  };

  // Handle form close
  const handleFormClose = () => {
    setShowAddForm(false);
    setEditingMethod(null);
  };

  // Clear error when component mounts
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [error, clearError]);

  return (
    <div className="space-y-6">
      <Card className="bg-zinc-800 border-zinc-700">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Payment Methods
          </CardTitle>
          <Button
            onClick={() => setShowAddForm(true)}
            className="bg-lime-600 hover:bg-lime-700 text-black"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Method
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            {paymentMethods.length === 0 ? (
              <div className="flex flex-col gap-1 text-center py-8">
                <CreditCard className="w-12 h-12 text-zinc-500 mx-auto mb-4" />
                <p className="text-zinc-400 mb-2">
                  No payment methods added yet
                </p>
                <p className="text-sm text-zinc-500">
                  Add a payment method to get started
                </p>
              </div>
            ) : (
              paymentMethods.map((method: PaymentMethod) => (
                <PaymentMethodCard
                  key={method.id}
                  method={method}
                  onSetDefault={handleSetDefault}
                  onDelete={handleDelete}
                  onEdit={handleEdit}
                />
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {showAddForm && (
        <AddPaymentMethodForm
          mode={editingMethod ? "edit" : "create"}
          editingMethod={editingMethod}
          onClose={handleFormClose}
          onAdd={handleAddPaymentMethod}
          onUpdate={handleUpdatePaymentMethod}
        />
      )}

      <SecuritySection />
    </div>
  );
};
