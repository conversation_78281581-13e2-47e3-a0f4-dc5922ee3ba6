"use client";

import { useEffect, useState } from "react";
import { DatabaseCrypto } from "@/lib/crypto/middleware";
import {
  ProposalForm,
  type ProposalFormData,
} from "@/components/view/proposals/form";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ProposalEditDialogProps {
  proposal: Proposal;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: Partial<Proposal>) => void;
  isUpdating: boolean;
}

export function ProposalEditDialog({
  proposal,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: ProposalEditDialogProps) {
  const [formData, setFormData] = useState<ProposalFormData | null>(null);

  async function decryptProposalDescription() {
    // Decrypt the description
    const convertedData: ProposalFormData = {
      id: proposal.id || "",
      createdAt: proposal.createdAt,
      updatedAt: proposal.updatedAt,
      name: proposal.name || "",
      description: proposal.description || "",
      budgetType: "fixed", // Default to fixed, could be enhanced to detect from data
      totalBudget: proposal.total_budget || 0,
      fixedBudget: proposal.fixed_budget || proposal.total_budget || 0,
      milestones: proposal.milestones || [],
      duration: proposal.duration || 1,
      agreedToTerms: proposal.agreed_to_terms_and_conditions || false,
      links: proposal.links || [],
    };

    try {
      let decryptedProposal = await DatabaseCrypto.decryptFields(
        convertedData,
        ["description"]
      );

      setFormData(decryptedProposal);
    } catch (error: any) {
      console.error("Failed to decrypt proposal description:", error);
    }
  }

  useEffect(() => {
    decryptProposalDescription();
  }, [proposal]);

  const handleFormSubmit = (data: ProposalFormData) => {
    // Convert ProposalFormData back to Proposal format
    const proposalData: Partial<Proposal> = {
      name: data.name,
      description: data.description,
      fixed_budget: data.budgetType === "fixed" ? data.fixedBudget : 0,
      total_budget:
        data.budgetType === "fixed"
          ? data.fixedBudget
          : data.milestones.reduce((sum, m) => sum + m.amount, 0),
      duration: data.duration,
      agreed_to_terms_and_conditions: data.agreedToTerms,
    };

    onSubmit(proposalData);
  };

  // Don't render until formData is ready
  if (!formData) {
    return null;
  }

  return (
    <ProposalForm
      editMode
      data={formData}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      onSubmit={handleFormSubmit}
      isCreating={isUpdating}
    />
  );
}
