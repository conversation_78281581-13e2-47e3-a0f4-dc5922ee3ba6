"use client";

import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { toast } from "sonner";
import type { RootState, AppDispatch } from "@/store";
import { fetcher, api } from "@/lib/common/requests";
import {
  createContract,
  updateContract,
  deleteContract,
} from "@/store/actions/contracts";
import {
  clearError,
  setCurrentContract,
  clearCurrentContract,
} from "@/store/slices/contract";
import type {
  Contract,
  CreateContract,
  UpdateContract,
} from "@/lib/api/validators/schemas/contract";

export function useContracts() {
  const dispatch = useDispatch<AppDispatch>();

  // Use SWR for data fetching instead of Redux selectors
  const {
    data: contractsData,
    error: contractsError,
    mutate: mutateContracts,
    isLoading: isLoadingContracts,
  } = useSWR("contract", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    mutate: mutateStatistics,
    isLoading: isLoadingStatistics,
  } = useSWR("contract/statistics", fetcher);

  // Only use Redux for current contract and operation states
  const {
    currentContract,
    isLoading: operationLoading,
    error: reduxError,
  } = useSelector((state: RootState) => state.contracts);

  // Extract data from SWR responses (API response format)
  const contracts = contractsData?.data || [];
  const statistics = statisticsData?.data || {
    total: 0,
    active: 0,
    completed: 0,
    draft: 0,
    terminated: 0,
    expired: 0,
    totalValue: 0,
    averageValue: 0,
  };

  // Combine errors from SWR and Redux
  const error = reduxError || contractsError || statisticsError;
  const isLoading =
    isLoadingContracts || isLoadingStatistics || operationLoading;

  // Refresh data functions using SWR mutate
  const refreshContracts = useCallback(() => {
    mutateContracts();
  }, [mutateContracts]);

  const refreshStatistics = useCallback(() => {
    mutateStatistics();
  }, [mutateStatistics]);

  const refreshData = useCallback(() => {
    mutateContracts();
    mutateStatistics();
  }, [mutateContracts, mutateStatistics]);

  // Create new contract
  const createNewContract = useCallback(
    async (contractData: CreateContract) => {
      try {
        const result = await dispatch(createContract(contractData));

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          mutateContracts(); // Refresh contracts data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to create contract";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateContracts, mutateStatistics]
  );

  // Update existing contract
  const updateExistingContract = useCallback(
    async (contractId: string, contractData: Partial<UpdateContract>) => {
      try {
        const result = await dispatch(
          updateContract({
            id: contractId,
            data: contractData,
          })
        );

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          mutateContracts(); // Refresh contracts data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to update contract";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateContracts, mutateStatistics]
  );

  // Delete contract
  const deleteExistingContract = useCallback(
    async (contractId: string) => {
      try {
        const result = await dispatch(deleteContract(contractId));

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          mutateContracts(); // Refresh contracts data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message || "Failed to delete contract";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutateContracts, mutateStatistics]
  );

  // Fetch single contract by ID
  const fetchSingleContract = useCallback(async (contractId: string) => {
    try {
      const response = await api.get(`contract/${contractId}`);
      if (response.success) {
        dispatch(setCurrentContract(response.data));
        return { success: true, data: response.data };
      } else {
        toast.error(response.message || "Failed to fetch contract");
        return { success: false, error: response.message };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch contract";
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  // Clear error
  const clearContractError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Set current contract
  const setContract = useCallback(
    (contract: Contract | null) => {
      dispatch(setCurrentContract(contract));
    },
    [dispatch]
  );

  // Clear current contract
  const clearContract = useCallback(() => {
    dispatch(clearCurrentContract());
  }, [dispatch]);

  // Initialize contracts data using SWR
  const initializeContracts = useCallback(() => {
    refreshData();
  }, [refreshData]);

  return {
    // State (from SWR)
    contracts,
    currentContract,
    statistics,
    isLoading,
    error,

    // Data refresh actions (SWR-based)
    refreshContracts,
    refreshStatistics,
    refreshData,

    // CRUD actions (Redux-based with SWR refresh)
    fetchContract: fetchSingleContract,
    createContract: createNewContract,
    updateContract: updateExistingContract,
    deleteContract: deleteExistingContract,
    initializeContracts,

    // Utility actions
    clearError: clearContractError,
    setCurrentContract: setContract,
    clearCurrentContract: clearContract,
  };
}

// Hook for getting a specific contract using SWR
export function useContract(contractId: string) {
  const {
    data: contractData,
    error,
    mutate,
    isLoading,
  } = useSWR(contractId ? `contract/${contractId}` : null, fetcher);

  return {
    contract: contractData?.data,
    error,
    isLoading,
    refresh: mutate,
  };
}

// Hook for contract statistics using SWR
export function useContractStatistics() {
  const {
    data: statisticsData,
    error,
    mutate,
    isLoading,
  } = useSWR("contract/statistics", fetcher);

  return {
    statistics: statisticsData?.data || {
      total: 0,
      active: 0,
      completed: 0,
      draft: 0,
      terminated: 0,
      expired: 0,
      totalValue: 0,
      averageValue: 0,
    },
    error,
    isLoading,
    refresh: mutate,
  };
}
