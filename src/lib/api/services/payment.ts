import { BaseService } from "./base";
import { prisma } from "@/lib/common/prisma";
import { DatabaseOptions } from "./base";
import {
  CreatePaymentMethodSchema,
  UpdatePaymentMethodSchema,
  SetDefaultPaymentMethodSchema,
  type CreatePaymentMethod,
  type UpdatePaymentMethod,
  type PaymentMethod,
} from "@/lib/api/validators/payment";
import { BraintreeService } from "./braintree";

/**
 * Payment Method Service
 *
 * Handles CRUD operations for payment methods including:
 * - Creating new payment methods
 * - Retrieving payment methods for authenticated users
 * - Updating existing payment methods
 * - Deleting payment methods
 * - Setting default payment methods
 * - Validating payment method data
 */
export class PaymentMethodService extends BaseService {
  private authRequired: boolean;

  constructor(config: any = {}) {
    super(config);
    this.authRequired = config.requireAuth ?? true;
    // Set the Prisma model for database operations
    this.setModel((prisma as any).payment_method);
  }

  /**
   * Get all payment methods for the authenticated user
   * @returns Array of payment methods
   */
  async getPaymentMethods(): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Fetch payment methods from database
      const options: DatabaseOptions = {
        where: {
          accountId: account.id,
        },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          isDefault: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethods = await this.findManyRecords<PaymentMethod>(options);

      // Transform to match API response format
      const transformedPaymentMethods = paymentMethods.map((method: any) => ({
        id: method.id,
        holder_name: method.holder_name,
        ref_number: method.ref_number,
        expiry_month: method.expiry_month,
        expiry_year: method.expiry_year,
        cvv: method.cvv,
        type: method.type,
        isDefault: method.isDefault,
        accountId: method.accountId,
        createdAt: method.createdAt,
        updatedAt: method.updatedAt,
      }));

      return transformedPaymentMethods;
    }, "getPaymentMethods");
  }

  /**
   * Get a specific payment method by ID
   * @param id - Payment method ID
   * @returns Payment method or null if not found
   */
  async getPaymentMethod(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Fetch payment method from database with access control
      const options: DatabaseOptions = {
        where: {
          id: id,
          accountId: account.id, // Ensure user can only access their own payment methods
        },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          isDefault: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethod = await this.findUniqueRecord<PaymentMethod>(options);

      if (!paymentMethod) {
        throw new Error("Payment method not found or access denied");
      }

      // Transform to match API response format
      return {
        id: paymentMethod.id,
        holder_name: paymentMethod.holder_name,
        ref_number: paymentMethod.ref_number,
        expiry_month: paymentMethod.expiry_month,
        expiry_year: paymentMethod.expiry_year,
        cvv: paymentMethod.cvv,
        type: paymentMethod.type,
        isDefault: paymentMethod.isDefault,
        accountId: paymentMethod.accountId,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
      };
    }, "getPaymentMethod");
  }

  /**
   * Create a new payment method
   * @param data - Payment method data
   * @returns Created payment method
   */
  async createPaymentMethod(data: CreatePaymentMethod): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(CreatePaymentMethodSchema, data);

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      const braintreeService = new BraintreeService();
      const paymentMethodNonce = await braintreeService.createPaymentMethod(
        validatedData.paymentMethodToken
      );

      // Create payment method in database
      const options: DatabaseOptions = {
        data: {
          holder_name: validatedData.holder_name,
          ref_number: validatedData.ref_number,
          expiry_month: validatedData.expiry_month,
          expiry_year: validatedData.expiry_year,
          cvv: validatedData.cvv,
          type: validatedData.type,
          isDefault: validatedData.isDefault,
          accountId: account.id,
        },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          isDefault: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethod = await this.createRecord<PaymentMethod>(options);

      // Transform to match API response format
      return {
        id: paymentMethod.id,
        holder_name: paymentMethod.holder_name,
        ref_number: paymentMethod.ref_number,
        expiry_month: paymentMethod.expiry_month,
        expiry_year: paymentMethod.expiry_year,
        cvv: paymentMethod.cvv,
        type: paymentMethod.type,
        isDefault: paymentMethod.isDefault,
        accountId: paymentMethod.accountId,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
      };
    }, "createPaymentMethod");
  }

  /**
   * Update an existing payment method
   * @param id - Payment method ID
   * @param data - Updated payment method data
   * @returns Updated payment method
   */
  async updatePaymentMethod(
    id: string,
    data: UpdatePaymentMethod
  ): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(UpdatePaymentMethodSchema, data);

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Check if payment method exists and belongs to user
      const existingPaymentMethod = await this.findUniqueRecord({
        where: {
          id: id,
          accountId: account.id,
        },
      });

      if (!existingPaymentMethod) {
        throw new Error("Payment method not found or access denied");
      }

      // Update payment method in database
      const options: DatabaseOptions = {
        where: {
          id: id,
          accountId: account.id,
        },
        data: validatedData,
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          isDefault: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethod = await this.updateRecord<PaymentMethod>(options);

      // Transform to match API response format
      return {
        id: paymentMethod.id,
        holder_name: paymentMethod.holder_name,
        ref_number: paymentMethod.ref_number,
        expiry_month: paymentMethod.expiry_month,
        expiry_year: paymentMethod.expiry_year,
        cvv: paymentMethod.cvv,
        type: paymentMethod.type,
        isDefault: paymentMethod.isDefault,
        accountId: paymentMethod.accountId,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
      };
    }, "updatePaymentMethod");
  }

  /**
   * Set a payment method as default
   * @param id - Payment method ID
   * @returns Updated payment method
   */
  async setDefaultPaymentMethod(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate input data
      const validatedData = this.validateInput(SetDefaultPaymentMethodSchema, {
        id,
      });

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // First, unset all other payment methods as default
      await prisma.payment_method.updateMany({
        where: {
          accountId: account.id,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });

      // Then set the specified payment method as default
      const options: DatabaseOptions = {
        where: {
          id: validatedData.id,
          accountId: account.id,
        },
        data: {
          isDefault: true,
        },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          isDefault: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethod = await this.updateRecord<PaymentMethod>(options);

      if (!paymentMethod) {
        throw new Error("Payment method not found or access denied");
      }

      // Transform to match API response format
      return {
        id: paymentMethod.id,
        holder_name: paymentMethod.holder_name,
        ref_number: paymentMethod.ref_number,
        expiry_month: paymentMethod.expiry_month,
        expiry_year: paymentMethod.expiry_year,
        cvv: paymentMethod.cvv,
        type: paymentMethod.type,
        isDefault: paymentMethod.isDefault,
        accountId: paymentMethod.accountId,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
      };
    }, "setDefaultPaymentMethod");
  }

  /**
   * Delete a payment method
   * @param id - Payment method ID
   * @returns Deleted payment method
   */
  async deletePaymentMethod(id: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Check if payment method exists and belongs to user
      const existingPaymentMethod = await this.findUniqueRecord({
        where: {
          id: id,
          accountId: account.id,
        },
      });

      if (!existingPaymentMethod) {
        throw new Error("Payment method not found or access denied");
      }

      // Delete payment method from database
      const options: DatabaseOptions = {
        where: {
          id: id,
          accountId: account.id,
        },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethod = await this.deleteRecord<PaymentMethod>(options);

      // Transform to match API response format
      return {
        id: paymentMethod.id,
        holder_name: paymentMethod.holder_name,
        ref_number: paymentMethod.ref_number,
        expiry_month: paymentMethod.expiry_month,
        expiry_year: paymentMethod.expiry_year,
        cvv: paymentMethod.cvv,
        type: paymentMethod.type,
        accountId: paymentMethod.accountId,
        createdAt: paymentMethod.createdAt,
        updatedAt: paymentMethod.updatedAt,
      };
    }, "deletePaymentMethod");
  }

  /**
   * Get payment methods count for the authenticated user
   * @returns Count of payment methods
   */
  async getPaymentMethodsCount(): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Count payment methods
      const options: DatabaseOptions = {
        where: {
          accountId: account.id,
        },
      };

      const count = await this.countRecords(options);

      return { count };
    }, "getPaymentMethodsCount");
  }

  /**
   * Get payment methods by type for the authenticated user
   * @param type - Payment method type
   * @returns Array of payment methods of specified type
   */
  async getPaymentMethodsByType(type: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Validate type
      const validTypes = [
        "visa",
        "mastercard",
        "amex",
        "discover",
        "paypal",
        "bank",
        "apple",
        "google",
      ];
      if (!validTypes.includes(type)) {
        throw new Error("Invalid payment method type");
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Fetch payment methods by type
      const options: DatabaseOptions = {
        where: {
          accountId: account.id,
          type: type,
        },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethods = await this.findManyRecords<PaymentMethod>(options);

      // Transform to match API response format
      const transformedPaymentMethods = paymentMethods.map((method: any) => ({
        id: method.id,
        holder_name: method.holder_name,
        ref_number: method.ref_number,
        expiry_month: method.expiry_month,
        expiry_year: method.expiry_year,
        cvv: method.cvv,
        type: method.type,
        accountId: method.accountId,
        createdAt: method.createdAt,
        updatedAt: method.updatedAt,
      }));

      return transformedPaymentMethods;
    }, "getPaymentMethodsByType");
  }

  /**
   * Validate payment method data without saving
   * @param data - Payment method data to validate
   * @returns Validation result
   */
  async validatePaymentMethodData(data: CreatePaymentMethod): Promise<any> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(CreatePaymentMethodSchema, data);

      return {
        isValid: true,
        data: validatedData,
        message: "Payment method data is valid",
      };
    }, "validatePaymentMethodData");
  }

  /**
   * Get payment method statistics for the authenticated user
   * @returns Payment method statistics
   */
  async getPaymentMethodStatistics(): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Get total count
      const totalCount = await this.countRecords({
        where: { accountId: account.id },
      });

      // Get count by type
      const typeStats = await Promise.all([
        this.countRecords({ where: { accountId: account.id, type: "visa" } }),
        this.countRecords({
          where: { accountId: account.id, type: "mastercard" },
        }),
        this.countRecords({ where: { accountId: account.id, type: "amex" } }),
        this.countRecords({
          where: { accountId: account.id, type: "discover" },
        }),
        this.countRecords({ where: { accountId: account.id, type: "paypal" } }),
        this.countRecords({ where: { accountId: account.id, type: "bank" } }),
        this.countRecords({ where: { accountId: account.id, type: "apple" } }),
        this.countRecords({ where: { accountId: account.id, type: "google" } }),
      ]);

      return {
        total: totalCount,
        byType: {
          visa: typeStats[0],
          mastercard: typeStats[1],
          amex: typeStats[2],
          discover: typeStats[3],
          paypal: typeStats[4],
          bank: typeStats[5],
          apple: typeStats[6],
          google: typeStats[7],
        },
      };
    }, "getPaymentMethodStatistics");
  }

  /**
   * Bulk delete payment methods
   * @param ids - Array of payment method IDs
   * @returns Delete result
   */
  async bulkDeletePaymentMethods(ids: string[]): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      if (!ids || ids.length === 0) {
        throw new Error("No payment method IDs provided");
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Delete payment methods
      const options: DatabaseOptions = {
        where: {
          id: { in: ids },
          accountId: account.id,
        },
      };

      const result = await this.deleteManyRecords(options);

      return {
        deletedCount: result.count,
        message: `Successfully deleted ${result.count} payment method(s)`,
      };
    }, "bulkDeletePaymentMethods");
  }

  /**
   * Search payment methods by holder name
   * @param query - Search query
   * @returns Array of matching payment methods
   */
  async searchPaymentMethods(query: string): Promise<any> {
    return this.executeOperation(async () => {
      // Require authentication if enabled
      if (this.authRequired) {
        this.requireAuth();
      }

      if (!query || query.trim().length === 0) {
        throw new Error("Search query is required");
      }

      // Get current user's account
      const account = await this.getCurrentUserAccount();

      // Search payment methods
      const options: DatabaseOptions = {
        where: {
          accountId: account.id,
          holder_name: {
            contains: query.trim(),
            mode: "insensitive",
          },
        },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          holder_name: true,
          ref_number: true,
          expiry_month: true,
          expiry_year: true,
          cvv: true,
          type: true,
          accountId: true,
          createdAt: true,
          updatedAt: true,
        },
      };

      const paymentMethods = await this.findManyRecords<PaymentMethod>(options);

      // Transform to match API response format
      const transformedPaymentMethods = paymentMethods.map((method: any) => ({
        id: method.id,
        holder_name: method.holder_name,
        ref_number: method.ref_number,
        expiry_month: method.expiry_month,
        expiry_year: method.expiry_year,
        cvv: method.cvv,
        type: method.type,
        accountId: method.accountId,
        createdAt: method.createdAt,
        updatedAt: method.updatedAt,
      }));

      return {
        query,
        results: transformedPaymentMethods,
        count: transformedPaymentMethods.length,
      };
    }, "searchPaymentMethods");
  }
}
