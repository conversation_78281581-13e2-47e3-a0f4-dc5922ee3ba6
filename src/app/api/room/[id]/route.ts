"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoomService } from "@/lib/api/services/room";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize room service
    const session: any = await auth();
    const roomService = new RoomService();

    // Set the service context with session and request
    roomService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse query parameters for include options
    const { searchParams } = new URL(request.url);
    const includeRelations = searchParams.get("includeRelations") !== "false";

    // Use the service to get room by ID
    const result = await roomService.getRoomById(params.id, includeRelations);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Room GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize room service
    const session: any = await auth();
    const roomService = new RoomService();

    // Set the service context with session and request
    roomService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const body = await request.json();

    // Use the service to update room
    const result = await roomService.updateRoom(params.id, body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Room PUT error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize room service
    const session: any = await auth();
    const roomService = new RoomService();

    // Set the service context with session and request
    roomService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to delete room
    const result = await roomService.deleteRoom(params.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Room DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
