"use client";

/**
 * Settings Container Component
 *
 * Main settings component with tabbed interface for Profile, RBAC, Notifications, and About.
 * This component is moved from the page component to maintain consistency with the view structure.
 */

import React from "react";
import { SettingsProvider, useSettings } from "./SettingsContext";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/common/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { ProfileTab } from "./ProfileTab";
import { PaymentTab } from "./PaymentTab";
import { RBACTab } from "./RBACTab";
import { NotificationsTab } from "./NotificationsTab";
import { AboutTab } from "./AboutTab";
import { User, Shield, Bell, Info } from "lucide-react";

// Inner component that uses the settings context
function SettingsContent() {
  const { activeTab, setActiveTab } = useSettings();

  return (
    <div className="container mx-auto flex flex-col gap-8 p-6 max-w-6xl">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground mt-2">
          Manage your account settings, roles, and preferences.
        </p>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="payment">Payment</TabsTrigger>
          <TabsTrigger value="rbac">RBAC</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Settings
              </CardTitle>
              <CardDescription>
                Manage your personal information and profile details.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfileTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Payment
              </CardTitle>
              <CardDescription>Manage your payment methods.</CardDescription>
            </CardHeader>
            <CardContent>
              <PaymentTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rbac">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role-Based Access Control
              </CardTitle>
              <CardDescription>
                Manage user roles, permissions, and access control settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RBACTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>
                Configure what types of alerts and notifications you want to
                receive.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NotificationsTab />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                About
              </CardTitle>
              <CardDescription>
                Information about the application and company.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AboutTab />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Main export component that provides the settings context
export function SettingsContainer() {
  return (
    <SettingsProvider>
      <SettingsContent />
    </SettingsProvider>
  );
}
