"use client";

import { useState, useCallback, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { fetcher } from "@/lib/common/requests";
import type { RootState, AppDispatch } from "@/store";
import {
  sendMessage,
  updateMessage,
  deleteMessage,
  createRoom,
  updateRoom,
  deleteRoom,
  updateUserState,
  fetchChatStatistics,
} from "@/store/actions/chat";
import {
  setCurrentRoom,
  clearError,
  addMessageOptimistic,
  markMessagesAsRead,
} from "@/store/slices/chat";
import type {
  CreateRoom,
  CreateMessage,
  UserState,
} from "@/lib/api/validators/schemas/chat";
import { useAuth } from "./useAuth";

export function useChat() {
  const dispatch = useDispatch<AppDispatch>();
  const [toReply, setToReply] = useState<any>(null);

  const {
    currentRoomId,
    // messages,
    typingUsers,
    userStates,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    isCreatingRoom,
    error,
  } = useSelector((state: RootState) => state.chat);

  const { user, accountId } = useAuth();

  // Use SWR for fetching rooms
  const {
    data: roomsData = [],
    error: roomsError,
    mutate: mutateRooms,
  } = useSWR(
    user?.id ? `chat/room?userId=${user.id}&includeMembers=true` : null,
    fetcher
  );

  // Use SWR for fetching rooms
  const {
    data: messagesData = [],
    error: messagesError,
    mutate: mutateMessages,
  } = useSWR(
    user?.id && currentRoomId ? `chat/room/${currentRoomId}/messages` : null,
    fetcher
  );

  const messages: any[] = messagesData?.data || [];

  // Use SWR for fetching statistics
  const { data: statistics, error: statsError } = useSWR(
    "chat/statistics",
    fetcher
  );

  const rooms = roomsData?.data?.rooms || [];
  // Get current room
  const currentRoom =
    rooms.find((room: any) => room.id === currentRoomId) || null;

  // Get messages for current room
  const currentMessages = messages;

  // Get typing users for current room
  const currentTypingUsers = currentRoomId
    ? typingUsers[currentRoomId] || []
    : [];

  // Fetch all rooms for current user
  const fetchUserRooms = useCallback(async () => {
    try {
      await mutateRooms();
    } catch (error) {
      console.error("Failed to fetch rooms:", error);
      throw error;
    }
  }, [mutateRooms]);

  // ============================================================================
  // INITIALIZATION & SETUP
  // ============================================================================

  // Initialize chat (fetch rooms and statistics)
  const initializeChat = useCallback(async () => {
    try {
      await Promise.all([
        mutateRooms(),
        dispatch(fetchChatStatistics()).unwrap(),
      ]);

      // Set user as online
      if (user?.id) {
        await updateCurrentUserState("online");
      }
    } catch (error) {
      console.error("Failed to initialize chat:", error);
    }
  }, [mutateRooms, dispatch, user?.id]);

  // Clear error
  const clearChatError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // ============================================================================
  // ROOM MANAGEMENT
  // ============================================================================

  // Create a new room
  const createChatRoom = useCallback(
    async (roomData: CreateRoom) => {
      try {
        const newRoom = await dispatch(createRoom(roomData)).unwrap();
        // Automatically switch to the new room
        dispatch(setCurrentRoom(newRoom.id));
        // Refresh rooms list
        await mutateRooms();
        return newRoom;
      } catch (error) {
        console.error("Failed to create room:", error);
        throw error;
      }
    },
    [dispatch, mutateRooms]
  );

  // Update an existing room
  const updateChatRoom = useCallback(
    async (roomId: string, roomData: Partial<CreateRoom>) => {
      try {
        const updatedRoom = await dispatch(
          updateRoom({ id: roomId, roomData })
        ).unwrap();
        // Refresh rooms list to show updated data
        await mutateRooms();
        return updatedRoom;
      } catch (error) {
        console.error("Failed to update room:", error);
        throw error;
      }
    },
    [dispatch, mutateRooms]
  );

  // Delete a room
  const deleteChatRoom = useCallback(
    async (roomId: string) => {
      try {
        await dispatch(deleteRoom(roomId)).unwrap();

        // If the deleted room was the current room, clear current room
        if (currentRoomId === roomId) {
          dispatch(setCurrentRoom(null));
        }

        // Refresh rooms list
        await mutateRooms();
        return { success: true, message: "Room deleted successfully" };
      } catch (error) {
        console.error("Failed to delete room:", error);
        throw error;
      }
    },
    [dispatch, currentRoomId, mutateRooms]
  );

  // ============================================================================
  // MESSAGE MANAGEMENT
  // ============================================================================

  function scrollToRepliedMessage(message: any) {
    const repliedMessage = document.querySelector(`#message-${message.id}`);

    if (repliedMessage) {
      repliedMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }

  // Fetch messages for a specific room (placeholder - requires API implementation)
  const fetchRoomMessages = useCallback(
    async (roomId: string, limit = 50, offset = 0) => {
      try {
        // For now, this is a placeholder
      } catch (error) {
        console.error("Failed to fetch messages:", error);
        throw error;
      }
    },
    []
  );

  function serializeMessageWithAttchments(
    message: any,
    attachments: any[]
  ): FormData | undefined {
    if (!message || !attachments) return;

    let formData = new FormData();
    attachments.forEach((file: File) => {
      formData.append("attachment-" + file.name, file, file.name);
    });
    Object.entries(message).forEach(([key, value]: [string, any]) => {
      formData.append(key, value);
    });
    return formData;
  }

  // Send a message
  const sendChatMessage = useCallback(
    async (content: string, reply?: any, attachments?: any[]) => {
      if (!accountId || !content.trim()) return;

      if (!currentRoomId) return;

      let roomId: string = currentRoomId;

      let data: CreateMessage | FormData | undefined = {
        content: content.trim(),
        sent_from: accountId,
        roomId: currentRoomId,
      };

      if (reply) {
        data.type = reply.type;
        data.associations = reply?.association;
      }

      let withAttachments = false;

      if (attachments && attachments?.length > 0) {
        data = serializeMessageWithAttchments(data, attachments);
        withAttachments = true;
      }

      try {
        // Send actual message - currently shows "This requires an API" toast
        await dispatch(sendMessage({ roomId, data, withAttachments })).unwrap();
        // Refresh messages list
      } catch (error) {
        throw error;
      } finally {
        mutateMessages();
        setToReply(null);
      }
    },
    [dispatch, user, currentRoomId]
  );

  // Update a message
  const updateChatMessage = useCallback(
    async (messageId: string, content: string) => {
      try {
        const updatedMessage = await dispatch(
          updateMessage({ messageId, content })
        ).unwrap();
        // Refresh messages list to show updated data
        await mutateMessages();
        return updatedMessage;
      } catch (error) {
        console.error("Failed to update message:", error);
        throw error;
      }
    },
    [dispatch, mutateMessages]
  );

  // Delete a message
  const deleteChatMessage = useCallback(
    async (messageId: string) => {
      try {
        await dispatch(deleteMessage(messageId)).unwrap();
        // Refresh messages list
        await mutateMessages();
        return { success: true, message: "Message deleted successfully" };
      } catch (error) {
        console.error("Failed to delete message:", error);
        throw error;
      }
    },
    [dispatch, mutateMessages]
  );

  // Copy message content to clipboard
  const copyMessageToClipboard = useCallback(async (content: string) => {
    try {
      // Strip HTML tags from content
      const textContent = content.replace(/<[^>]*>/g, "").trim();
      await navigator.clipboard.writeText(textContent);
      return { success: true, message: "Message copied to clipboard" };
    } catch (error) {
      console.error("Failed to copy message:", error);
      throw new Error("Failed to copy message to clipboard");
    }
  }, []);

  // Set up reply to a message
  const replyToMessage = useCallback(
    (message: { id: string; content: string; sender?: any }) => {
      // Strip HTML tags for display
      let response = {
        type: "reply",
        association: [
          {
            id: message.id,
            entity: "message",
            content: message.content,
            sender: message.sender?.name || "Unknown",
          },
        ],
        displayContent: message.content.replace(/<[^>]*>/g, "").trim(),
        originalMessage: message,
      };

      setToReply(response);
      return response;
    },
    []
  );

  const closeReply = useCallback(() => {
    setToReply(null);
  }, []);

  // ============================================================================
  // NAVIGATION & ROOM SELECTION
  // ============================================================================

  // Set current room
  const selectRoom = useCallback(
    (roomId: string | null) => {
      dispatch(setCurrentRoom(roomId));

      // Fetch messages for the selected room
      if (roomId) {
        fetchRoomMessages(roomId);
        // Mark messages as read
        dispatch(markMessagesAsRead(roomId));
      }
    },
    [dispatch, fetchRoomMessages]
  );

  // ============================================================================
  // USER STATE MANAGEMENT
  // ============================================================================

  // Update user state (online/offline/typing/away)
  const updateCurrentUserState = useCallback(
    async (state: UserState, roomId?: string) => {
      if (!user?.id) return;

      try {
        await dispatch(
          updateUserState({
            userId: user.id,
            state,
            roomId: roomId || currentRoomId || undefined,
          })
        ).unwrap();
      } catch (error) {
        console.error("Failed to update user state:", error);
      }
    },
    [dispatch, user?.id, currentRoomId]
  );

  // Start typing indicator
  const startTyping = useCallback(
    async (roomId?: string) => {
      const targetRoomId = roomId || currentRoomId;
      if (targetRoomId) {
        await updateCurrentUserState("typing", targetRoomId);
      }
    },
    [updateCurrentUserState, currentRoomId]
  );

  // Stop typing indicator
  const stopTyping = useCallback(
    async (roomId?: string) => {
      const targetRoomId = roomId || currentRoomId;
      if (targetRoomId) {
        await updateCurrentUserState("online", targetRoomId);
      }
    },
    [updateCurrentUserState, currentRoomId]
  );

  // ============================================================================
  // STATISTICS & UTILITIES
  // ============================================================================

  // Fetch statistics
  const fetchStats = useCallback(async () => {
    try {
      await dispatch(fetchChatStatistics()).unwrap();
    } catch (error) {
      console.error("Failed to fetch statistics:", error);
      throw error;
    }
  }, [dispatch]);

  // Auto-initialize when user is available
  useEffect(() => {
    if (user?.id) {
      initializeChat();
      // Set user as online
      updateCurrentUserState("online");
    }
  }, [user?.id, initializeChat, updateCurrentUserState]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (user?.id) {
        // Set user as offline when component unmounts
        updateCurrentUserState("offline");
      }
    };
  }, [user?.id, updateCurrentUserState]);

  return {
    // State
    toReply,
    rooms: rooms || [],
    currentRoom,
    currentRoomId,
    messages: currentMessages,
    allMessages: messages,
    typingUsers: currentTypingUsers,
    allTypingUsers: typingUsers,
    userStates,
    statistics: statistics?.data || null,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    isCreatingRoom,
    error: error || roomsError || statsError,

    // Actions
    fetchRooms: fetchUserRooms,
    fetchMessages: fetchRoomMessages,
    sendMessage: sendChatMessage,
    updateMessage: updateChatMessage,
    deleteMessage: deleteChatMessage,
    copyMessage: copyMessageToClipboard,
    replyToMessage: replyToMessage,
    closeReply,
    createRoom: createChatRoom,
    updateRoom: updateChatRoom,
    deleteRoom: deleteChatRoom,
    selectRoom,
    updateUserState: updateCurrentUserState,
    startTyping,
    stopTyping,
    fetchStatistics: fetchStats,
    scrollToRepliedMessage,
    initializeChat,

    // Utility actions
    clearError: clearChatError,
    markAsRead: (roomId: string) => dispatch(markMessagesAsRead(roomId)),

    // Computed values
    hasRooms: (rooms || []).length > 0,
    hasMessages: currentMessages.length > 0,
    isAnyLoading:
      isLoading || isLoadingMessages || isSendingMessage || isCreatingRoom,
    currentUser: { id: accountId },
  };
}
