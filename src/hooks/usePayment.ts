"use client";

import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import useSWR from "swr";
import type { RootState, AppDispatch } from "@/store";
import { fetcher } from "@/lib/common/requests";
import {
  createPaymentMethod,
  updatePaymentMethod,
  setDefaultPaymentMethod,
  deletePaymentMethod,
  bulkDeletePaymentMethods,
  searchPaymentMethods,
  fetchPaymentMethodsByType,
  validatePaymentMethodData,
} from "@/store/actions/payment";
import {
  clearError,
  setCurrentPaymentMethod,
  clearCurrentPaymentMethod,
  clearSearchResults,
  clearFilteredPaymentMethods,
  clearValidationResult,
  setLoading,
} from "@/store/slices/payment";
import type {
  PaymentMethod,
  CreatePaymentMethod,
  UpdatePaymentMethod,
} from "@/lib/api/validators/payment";
import { useAuth } from "./useAuth";

export function usePayment() {
  const dispatch = useDispatch<AppDispatch>();
  let { paymentMethods: UserPaymentMethods } = useAuth();

  // Use SWR for data fetching instead of Redux selectors for better caching
  const {
    data: paymentMethodsData,
    error: paymentMethodsError,
    mutate: mutatePaymentMethods,
    isLoading: isLoadingPaymentMethods,
  } = useSWR("payment", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    mutate: mutateStatistics,
    isLoading: isLoadingStatistics,
  } = useSWR("payment?stats=true", fetcher);

  // Only use Redux for current payment method, search results, and operation states
  const {
    currentPaymentMethod,
    searchResults,
    filteredPaymentMethods,
    validationResult,
    isLoading: operationLoading,
    error: reduxError,
  } = useSelector((state: RootState) => state.payment);

  // Transform backend data to frontend format
  const transformPaymentMethod = (backendMethod: any) => ({
    id: backendMethod.id,
    type: mapBackendTypeToFrontend(backendMethod.type),
    provider: backendMethod.type, // Backend type is actually the provider
    last4: backendMethod.ref_number,
    cvv: backendMethod.cvv,
    expiryMonth: backendMethod.expiry_month,
    expiryYear: backendMethod.expiry_year,
    holderName: backendMethod.holder_name,
    isDefault: backendMethod.isDefault || false, // Use actual isDefault field from backend
    isVerified: true, // TODO: Add verification logic when backend supports it
    createdAt: backendMethod.createdAt,
    // Add optional fields based on type
    ...(backendMethod.type === "paypal" && {
      email: backendMethod.holder_name,
    }), // Placeholder
    ...(backendMethod.type === "bank" && {
      bankName: "Bank", // Placeholder - backend doesn't store bank name yet
      accountType: "checking" as const,
    }),
  });

  // Helper function to map backend types to frontend types
  const mapBackendTypeToFrontend = (backendType: string) => {
    switch (backendType) {
      case "visa":
      case "mastercard":
      case "amex":
      case "discover":
        return "credit_card" as const;
      case "paypal":
        return "paypal" as const;
      case "bank":
        return "bank_account" as const;
      case "apple":
        return "apple_pay" as const;
      case "google":
        return "google_pay" as const;
      default:
        return "credit_card" as const;
    }
  };

  // Extract and transform data from SWR responses
  let paymentMethods =
    (paymentMethodsData?.data || []).map(transformPaymentMethod) ||
    UserPaymentMethods;

  // Extract statistics from SWR response
  const statistics = statisticsData?.data || {
    total: 0,
    byType: {
      visa: 0,
      mastercard: 0,
      amex: 0,
      discover: 0,
      paypal: 0,
      bank: 0,
      apple: 0,
      google: 0,
    },
  };

  // Combine errors from SWR and Redux
  const error = reduxError || paymentMethodsError || statisticsError;
  const isLoading =
    isLoadingPaymentMethods || isLoadingStatistics || operationLoading;

  // Refresh data functions using SWR mutate
  const refreshPaymentMethods = useCallback(() => {
    mutatePaymentMethods();
  }, [mutatePaymentMethods]);

  const refreshStatistics = useCallback(() => {
    mutateStatistics();
  }, [mutateStatistics]);

  const refreshData = useCallback(() => {
    mutatePaymentMethods();
    mutateStatistics();
  }, [mutatePaymentMethods, mutateStatistics]);

  // Create new payment method
  const createNewPaymentMethod = useCallback(
    async (paymentMethodData: CreatePaymentMethod) => {
      try {
        const result = await dispatch(createPaymentMethod(paymentMethodData));

        // Handle response based on Redux action result pattern
        if (result.type.endsWith("/fulfilled")) {
          mutatePaymentMethods(); // Refresh payment methods data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message ||
            "Failed to create payment method";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutatePaymentMethods, mutateStatistics]
  );

  // Update existing payment method
  const updateExistingPaymentMethod = useCallback(
    async (id: string, paymentMethodData: UpdatePaymentMethod) => {
      try {
        const result = await dispatch(
          updatePaymentMethod({ id, data: paymentMethodData })
        );

        if (result.type.endsWith("/fulfilled")) {
          mutatePaymentMethods(); // Refresh payment methods data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message ||
            "Failed to update payment method";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutatePaymentMethods, mutateStatistics]
  );

  // Set default payment method
  const setDefaultExistingPaymentMethod = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(setDefaultPaymentMethod(id));

        if (result.type.endsWith("/fulfilled")) {
          mutatePaymentMethods(); // Refresh payment methods data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message ||
            "Failed to set default payment method";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutatePaymentMethods, mutateStatistics]
  );

  // Delete payment method
  const deleteExistingPaymentMethod = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(deletePaymentMethod(id));

        if (result.type.endsWith("/fulfilled")) {
          mutatePaymentMethods(); // Refresh payment methods data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message ||
            "Failed to delete payment method";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutatePaymentMethods, mutateStatistics]
  );

  // Bulk delete payment methods
  const bulkDeleteExistingPaymentMethods = useCallback(
    async (ids: string[]) => {
      try {
        const result = await dispatch(bulkDeletePaymentMethods(ids));

        if (result.type.endsWith("/fulfilled")) {
          mutatePaymentMethods(); // Refresh payment methods data
          mutateStatistics(); // Refresh statistics
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            (result.payload as any)?.message ||
            "Failed to delete payment methods";
          return { success: false, error: errorMessage };
        }
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch, mutatePaymentMethods, mutateStatistics]
  );

  // Search payment methods
  const searchExistingPaymentMethods = useCallback(
    async (query: string) => {
      try {
        const result = await dispatch(searchPaymentMethods(query));
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch]
  );

  // Fetch payment methods by type
  const fetchPaymentMethodsByTypeFilter = useCallback(
    async (type: string) => {
      try {
        const result = await dispatch(fetchPaymentMethodsByType(type));
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch]
  );

  // Validate payment method data
  const validatePaymentMethod = useCallback(
    async (paymentMethodData: CreatePaymentMethod) => {
      try {
        const result = await dispatch(
          validatePaymentMethodData(paymentMethodData)
        );
        return result;
      } catch (error) {
        throw error;
      }
    },
    [dispatch]
  );

  // Get single payment method by ID
  const getPaymentMethodById = useCallback(
    (id: string): PaymentMethod | undefined => {
      return paymentMethods.find((method: PaymentMethod) => method.id === id);
    },
    [paymentMethods]
  );

  // Set current payment method
  const setCurrentMethod = useCallback(
    (paymentMethod: PaymentMethod | null) => {
      dispatch(setCurrentPaymentMethod(paymentMethod));
    },
    [dispatch]
  );

  // Clear current payment method
  const clearCurrentMethod = useCallback(() => {
    dispatch(clearCurrentPaymentMethod());
  }, [dispatch]);

  // Clear search results
  const clearSearch = useCallback(() => {
    dispatch(clearSearchResults());
  }, [dispatch]);

  // Clear filtered payment methods
  const clearFiltered = useCallback(() => {
    dispatch(clearFilteredPaymentMethods());
  }, [dispatch]);

  // Clear validation result
  const clearValidation = useCallback(() => {
    dispatch(clearValidationResult());
  }, [dispatch]);

  // Clear error
  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Set loading state
  const setLoadingState = useCallback(
    (loading: boolean) => {
      dispatch(setLoading(loading));
    },
    [dispatch]
  );
  //
  return {
    // Data
    paymentMethods,
    currentPaymentMethod,
    statistics,
    searchResults,
    filteredPaymentMethods,
    validationResult,

    // State
    isLoading,
    error,

    // CRUD Operations
    createPaymentMethod: createNewPaymentMethod,
    updatePaymentMethod: updateExistingPaymentMethod,
    setDefaultPaymentMethod: setDefaultExistingPaymentMethod,
    deletePaymentMethod: deleteExistingPaymentMethod,
    bulkDeletePaymentMethods: bulkDeleteExistingPaymentMethods,

    // Search & Filter
    searchPaymentMethods: searchExistingPaymentMethods,
    fetchPaymentMethodsByType: fetchPaymentMethodsByTypeFilter,

    // Validation
    validatePaymentMethodData: validatePaymentMethod,

    // Utilities
    getPaymentMethodById,
    setCurrentPaymentMethod: setCurrentMethod,
    clearCurrentPaymentMethod: clearCurrentMethod,
    clearSearchResults: clearSearch,
    clearFilteredPaymentMethods: clearFiltered,
    clearValidationResult: clearValidation,
    clearError: clearErrorState,
    setLoading: setLoadingState,

    // Data refresh
    refreshPaymentMethods,
    refreshStatistics,
    refreshData,
  };
}
