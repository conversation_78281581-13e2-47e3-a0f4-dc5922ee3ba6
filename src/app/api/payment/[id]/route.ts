"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { PaymentMethodService } from "@/lib/api/services/payment";

// GET /api/payment/[id] - Get single payment method
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Payment method ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to get payment method
    const result = await paymentService.getPaymentMethod(params.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment method GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PATCH /api/payment/[id] - Update payment method
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Payment method ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Use the service to update payment method
    const result = await paymentService.updatePaymentMethod(
      params.id,
      updateData
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment method PATCH error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/payment/[id] - Full update payment method (alternative to PATCH)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Payment method ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Parse request body
    const updateData = await request.json();

    // Use the service to update payment method
    const result = await paymentService.updatePaymentMethod(
      params.id,
      updateData
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment method PUT error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/payment/[id] - Delete payment method
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize payment method service
    const session: any = await auth();
    const paymentService = new PaymentMethodService();

    // Set the service context with session and request
    paymentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "Payment method ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to delete payment method
    const result = await paymentService.deletePaymentMethod(params.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Payment method DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
