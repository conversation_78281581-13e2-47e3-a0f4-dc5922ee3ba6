"use client";

import { useState, useEffect } from "react";
import { redirect, useParams } from "next/navigation";
import HtmlParser from "react-html-parser";
import { useAuth } from "@/hooks/useAuth";
import { useContracts } from "@/hooks/useContracts";
import { CanRead } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { ContractHeader } from "./header";
import { ContractStatistics } from "./statistics";
import { ContractTable } from "./table";
import { ContractCreateDialog } from "./create-dialog";
import { type Contract } from "@/data/contracts-mock";
import type {
  Contract as ApiContract,
  CreateContract,
} from "@/lib/api/validators/schemas/contract";

// Type adapter to convert API contracts to UI contracts
const adaptApiContractToUI = (apiContract: ApiContract): Contract => {
  // Map API status to UI status
  const statusMap: Record<string, Contract["status"]> = {
    draft: "draft",
    active: "active",
    completed: "completed",
    terminated: "terminated",
    expired: "expired",
  };

  return {
    id: apiContract.id,
    title: apiContract.title,
    description: (apiContract.description as string) || "",
    status: statusMap[apiContract.status] || "draft",
    clientName: apiContract.client_id || "",
    contractValue: apiContract.total_value || 0,
    startDate: new Date(apiContract.start_date).toISOString(),
    endDate: new Date(apiContract.end_date).toISOString(),
    createdDate: new Date(apiContract.createdAt).toISOString(),
    lastModified: new Date(apiContract.updatedAt).toISOString(),
    proposalId: apiContract.proposal_id,
  };
};

export function ContractContainer() {
  const { slug } = useParams();
  const { user, personalizedRoute } = useAuth();

  const {
    contracts,
    statistics,
    isLoading,
    error,
    createContract,
    updateContract,
    deleteContract,
    clearError,
    initializeContracts,
  } = useContracts();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  useEffect(() => {
    // Initialize contracts data
    initializeContracts();
  }, [slug, initializeContracts]);

  useEffect(() => {
    if (error) {
      console.error("Contract error:", error);
    }
  }, [error]);

  // Clear error when component unmounts or when user interacts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleCreateContract = async (formData: CreateContract) => {
    try {
      await createContract(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create contract:", error);
    }
  };

  const handleUpdateContract = async (
    id: string,
    data: Partial<ApiContract>
  ) => {
    try {
      await updateContract(id, data);
    } catch (error) {
      console.error("Failed to update contract:", error);
    }
  };

  const handleDeleteContract = async (id: string) => {
    try {
      await deleteContract(id);
    } catch (error) {
      console.error("Failed to delete contract:", error);
    }
  };

  // Convert API contracts to UI contracts
  const uiContracts = contracts.map(adaptApiContractToUI);

  return (
    <CanRead entity={DEFAULT_ENTITIES.CONTRACT} resourceId={personalizedRoute}>
      <div className="grid grid-cols-1 gap-12 p-4">
        <ContractHeader
          onCreateContract={() => setIsCreateDialogOpen(true)}
          contractsCount={contracts.length}
        />

        <ContractStatistics statistics={statistics} isLoading={isLoading} />

        <ContractTable
          contracts={uiContracts}
          isLoading={isLoading}
          onUpdateContract={handleUpdateContract}
          onDeleteContract={handleDeleteContract}
        />

        {/* Create Contract Dialog */}
        <ContractCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onCreateContract={handleCreateContract}
          isLoading={isLoading}
        />
      </div>
    </CanRead>
  );
}
