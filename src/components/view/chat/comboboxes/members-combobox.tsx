"use client";

import { useState } from "react";
import { useUserManagement } from "@/hooks/useRBAC";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@/components/common/ui/combobox";
import { Badge } from "@/components/common/ui/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import { Button } from "@/components/common/ui/button";
import { Users, X, UserPlus, Building2 } from "lucide-react";
import { useContracts } from "@/hooks/useContracts";

interface Member {
  id: string;
  name?: string;
  email: string;
  image?: string;
}

interface MembersComboboxProps {
  value: Member[];
  onValueChange: (members: Member[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxMembers?: number;
  contractId?: string;
}

export function MembersCombobox({
  value = [],
  onValueChange,
  placeholder = "Add members...",
  disabled = false,
  maxMembers = 50,
  contractId,
}: MembersComboboxProps) {
  const { users, isLoading, error } = useUserManagement();
  const { contracts } = useContracts();
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  // Find the contract and its client if contractId is provided
  const selectedContract = contractId
    ? contracts.find((contract: any) => contract.id === contractId)
    : null;

  const contractClient = selectedContract
    ? users.find((user: any) => user.id === selectedContract.clientId)
    : null;

  // Filter out already selected members and search
  const availableUsers = users.filter((user: any) => {
    const isAlreadySelected = value.some((member) => member.id === user.id);
    const matchesSearch =
      searchValue === "" ||
      user.name?.toLowerCase().includes(searchValue.toLowerCase()) ||
      user.email.toLowerCase().includes(searchValue.toLowerCase());

    return !isAlreadySelected && matchesSearch;
  });

  const handleSelect = (user: any) => {
    if (value.length >= maxMembers) {
      return; // Don't add if max members reached
    }

    const newMember: Member = {
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image,
    };

    onValueChange([...value, newMember]);
    setSearchValue("");
    setOpen(false);
  };

  const handleRemove = (memberId: string) => {
    onValueChange(value.filter((member) => member.id !== memberId));
  };

  const handleClearAll = () => {
    onValueChange([]);
  };

  if (error) {
    return (
      <div className="text-sm text-red-600 dark:text-red-400">
        Failed to load users
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Contract Client Breadcrumb */}
      {contractClient && selectedContract && (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">
              Contract Client (Auto-added)
            </span>
          </div>

          <div className="flex items-center space-x-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md border border-blue-200 dark:border-blue-800">
            <Avatar className="h-6 w-6">
              <AvatarImage
                src={contractClient.image || ""}
                alt={contractClient.name || "Client"}
              />
              <AvatarFallback className="text-xs">
                {contractClient.name?.charAt(0)?.toUpperCase() ||
                  contractClient.email.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium">
                {contractClient.name || "Unnamed Client"}
              </span>
              <span className="text-xs text-muted-foreground">
                {contractClient.email}
              </span>
            </div>
            <Badge variant="outline" className="text-xs ml-auto">
              Client
            </Badge>
          </div>

          <div className="text-xs text-muted-foreground">
            This client will be automatically added as a member from the
            contract "{selectedContract.title}".
          </div>
        </div>
      )}

      {/* Selected Members Display */}
      {value.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                Selected Members ({value.length})
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="h-6 px-2 text-xs"
            >
              Clear All
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {value.map((member) => (
              <Badge
                key={member.id}
                variant="secondary"
                className="flex items-center space-x-2 pr-1 pl-2 py-1"
              >
                <Avatar className="h-5 w-5">
                  <AvatarImage
                    src={member.image || ""}
                    alt={member.name || "User"}
                  />
                  <AvatarFallback className="text-xs">
                    {member.name?.charAt(0)?.toUpperCase() ||
                      member.email.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col items-start">
                  <span className="text-xs font-medium">
                    {member.name || "Unnamed User"}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {member.email}
                  </span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemove(member.id)}
                  className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Add Members Combobox */}
      {value.length < maxMembers && (
        <Combobox open={open} onOpenChange={setOpen}>
          <ComboboxTrigger
            disabled={disabled || isLoading}
            placeholder={isLoading ? "Loading users..." : placeholder}
          >
            <div className="flex items-center space-x-2">
              <UserPlus className="h-4 w-4 text-muted-foreground" />
              <span>{placeholder}</span>
            </div>
          </ComboboxTrigger>

          <ComboboxContent className="w-[350px]">
            <ComboboxCommand>
              <ComboboxInput
                placeholder="Search users by name or email..."
                value={searchValue}
                onValueChange={setSearchValue}
              />
              <ComboboxList>
                <ComboboxEmpty>
                  {isLoading ? "Loading users..." : "No users found."}
                </ComboboxEmpty>

                <ComboboxGroup heading="Available Users">
                  {availableUsers.map((user: any) => (
                    <ComboboxItem
                      key={user.id}
                      value={user.id}
                      onSelect={() => handleSelect(user)}
                      className="flex items-center space-x-3 p-3"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={user.image || ""}
                          alt={user.name || "User"}
                        />
                        <AvatarFallback className="text-xs">
                          {user.name?.charAt(0)?.toUpperCase() ||
                            user.email.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex flex-col items-start flex-1">
                        <span className="font-medium">
                          {user.name || "Unnamed User"}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {user.email}
                        </span>
                        {user.role && (
                          <Badge variant="outline" className="text-xs mt-1">
                            {user.role.name}
                          </Badge>
                        )}
                      </div>

                      <ComboboxItemIndicator isSelected={false} />
                    </ComboboxItem>
                  ))}
                </ComboboxGroup>
              </ComboboxList>
            </ComboboxCommand>
          </ComboboxContent>
        </Combobox>
      )}

      {/* Max Members Warning */}
      {value.length >= maxMembers && (
        <div className="text-sm text-amber-600 dark:text-amber-400 flex items-center space-x-2">
          <Users className="h-4 w-4" />
          <span>Maximum number of members reached ({maxMembers})</span>
        </div>
      )}
    </div>
  );
}
