import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";
import {
  type CreateTransactionRequest,
  type CreateCustomerRequest,
  type CreatePaymentMethodRequest,
  type VoidTransactionRequest,
  type RefundTransactionRequest,
} from "@/lib/api/validators/schemas/braintree";

// Types for Braintree actions
export interface BraintreeTransaction {
  id: string;
  type: "sale" | "credit" | "void" | "refund";
  amount: number;
  status:
    | "pending"
    | "authorized"
    | "submitted_for_settlement"
    | "settled"
    | "voided"
    | "failed";
  customerId?: string;
  paymentMethodToken?: string;
  merchantAccountId?: string;
  orderId?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  processorResponseCode?: string;
  processorResponseText?: string;
}

export interface BraintreeCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BraintreePaymentMethod {
  token: string;
  customerId: string;
  isDefault: boolean;
  imageUrl?: string;
  cardType?: string;
  last4?: string;
  expirationMonth?: string;
  expirationYear?: string;
  cardholderName?: string;
  createdAt: string;
  updatedAt: string;
}

// Request interfaces are now imported from validators

// Initialize Braintree client
export const initializeBraintree = createAsyncThunk(
  "braintree/initialize",
  async (customerId: string | undefined, { rejectWithValue }) => {
    try {
      const response = await api.get("braintree", { customerId });

      if (response.success && !response.error) {
        toast.success("Braintree initialized successfully");
        return {
          clientToken: response.data.clientToken,
          isReady: true,
        };
      } else {
        toast.error(response.message || "Failed to initialize Braintree");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to initialize Braintree",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to initialize Braintree";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create transaction
export const createTransaction = createAsyncThunk(
  "braintree/createTransaction",
  async (request: CreateTransactionRequest, { rejectWithValue }) => {
    try {
      const response = await api.post("braintree", request);

      if (response.success && !response.error) {
        const transaction: BraintreeTransaction = {
          id: response.data.id,
          type: "sale",
          amount: response.data.amount,
          status: response.data.status,
          customerId: response.data.customerId,
          paymentMethodToken: response.data.paymentMethodToken,
          merchantAccountId: response.data.merchantAccountId,
          orderId: response.data.orderId,
          description: request.description,
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
          processorResponseCode: response.data.processorResponseCode,
          processorResponseText: response.data.processorResponseText,
        };

        toast.success(
          `Transaction of $${request.amount} completed successfully!`
        );
        return transaction;
      } else {
        toast.error(response.message || "Transaction failed");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Transaction failed",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Transaction failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create customer
export const createCustomer = createAsyncThunk(
  "braintree/createCustomer",
  async (request: CreateCustomerRequest, { rejectWithValue }) => {
    try {
      const response = await api.post("braintree", {
        action: "create_customer",
        ...request,
      });

      if (response.success && !response.error) {
        const customer: BraintreeCustomer = {
          id: response.data.id,
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          email: response.data.email,
          phone: response.data.phone,
          company: response.data.company,
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
        };

        toast.success("Customer created successfully!");
        return customer;
      } else {
        toast.error(response.message || "Failed to create customer");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create customer",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create customer";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create payment method
export const createPaymentMethod = createAsyncThunk(
  "braintree/createPaymentMethod",
  async (request: CreatePaymentMethodRequest, { rejectWithValue }) => {
    try {
      const response = await api.post("braintree", {
        action: "create_payment_method",
        ...request,
      });

      if (response.success && !response.error) {
        const paymentMethod: BraintreePaymentMethod = {
          token: response.data.token,
          customerId: response.data.customerId,
          isDefault: response.data.default,
          imageUrl: response.data.imageUrl,
          cardType: response.data.cardType,
          last4: response.data.last4,
          expirationMonth: response.data.expirationMonth,
          expirationYear: response.data.expirationYear,
          cardholderName: response.data.cardholderName,
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
        };

        toast.success("Payment method added successfully!");
        return paymentMethod;
      } else {
        toast.error(response.message || "Failed to add payment method");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to add payment method",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to add payment method";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Void transaction
export const voidTransaction = createAsyncThunk(
  "braintree/voidTransaction",
  async (request: VoidTransactionRequest, { rejectWithValue }) => {
    try {
      const response = await api.post(
        `braintree/transactions/${request.transactionId}/void`
      );

      if (response.success && !response.error) {
        toast.success("Transaction voided successfully!");
        return response.data;
      } else {
        toast.error(response.message || "Failed to void transaction");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to void transaction",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to void transaction";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Refund transaction
export const refundTransaction = createAsyncThunk(
  "braintree/refundTransaction",
  async (request: RefundTransactionRequest, { rejectWithValue }) => {
    try {
      const response = await api.post(
        `braintree/transactions/${request.transactionId}/refund`,
        {
          amount: request.amount,
        }
      );

      if (response.success && !response.error) {
        toast.success("Transaction refunded successfully!");
        return response.data;
      } else {
        toast.error(response.message || "Failed to refund transaction");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to refund transaction",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to refund transaction";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Test connection
export const testConnection = createAsyncThunk(
  "braintree/testConnection",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.post("braintree", { action: "test" });

      if (response.success && !response.error) {
        toast.success("Braintree connection successful!");
        return response.data;
      } else {
        toast.error(response.message || "Connection test failed");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Connection test failed",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Connection test failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Search transactions (automatically scoped to company merchant account)
export const searchTransactions = createAsyncThunk(
  "braintree/searchTransactions",
  async (
    searchParams: {
      customerId?: string;
      status?: string;
      amount?: { min?: number; max?: number };
      createdAt?: { min?: Date; max?: Date };
      orderId?: string;
      paymentMethodToken?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.post("braintree", {
        action: "search_transactions",
        searchParams,
      });

      if (response.success && !response.error) {
        return response.data || [];
      } else {
        toast.error(response.message || "Transaction search failed");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Transaction search failed",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Transaction search failed";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
