import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { PaymentMethod } from "@/lib/api/validators/payment";
import type { PaymentMethodStatistics } from "../actions/payment";
import {
  fetchPaymentMethods,
  fetchPaymentMethod,
  createPaymentMethod,
  updatePaymentMethod,
  deletePaymentMethod,
  bulkDeletePaymentMethods,
  fetchPaymentMethodStatistics,
  searchPaymentMethods,
  fetchPaymentMethodsByType,
  validatePaymentMethodData,
} from "../actions/payment";

interface PaymentMethodsState {
  paymentMethods: PaymentMethod[];
  currentPaymentMethod: PaymentMethod | null;
  statistics: PaymentMethodStatistics;
  searchResults: {
    query: string;
    results: PaymentMethod[];
    count: number;
  } | null;
  filteredPaymentMethods: PaymentMethod[];
  isLoading: boolean;
  error: string | null;
  validationResult: {
    isValid: boolean;
    data?: any;
    message?: string;
  } | null;
}

const initialStatistics: PaymentMethodStatistics = {
  total: 0,
  byType: {
    visa: 0,
    mastercard: 0,
    amex: 0,
    discover: 0,
    paypal: 0,
    bank: 0,
    apple: 0,
    google: 0,
  },
};

const initialState: PaymentMethodsState = {
  paymentMethods: [],
  currentPaymentMethod: null,
  statistics: initialStatistics,
  searchResults: null,
  filteredPaymentMethods: [],
  isLoading: false,
  error: null,
  validationResult: null,
};

const paymentSlice = createSlice({
  name: "payment",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    // Set current payment method
    setCurrentPaymentMethod: (
      state,
      action: PayloadAction<PaymentMethod | null>
    ) => {
      state.currentPaymentMethod = action.payload;
    },
    // Clear current payment method
    clearCurrentPaymentMethod: (state) => {
      state.currentPaymentMethod = null;
    },
    // Clear search results
    clearSearchResults: (state) => {
      state.searchResults = null;
    },
    // Clear filtered payment methods
    clearFilteredPaymentMethods: (state) => {
      state.filteredPaymentMethods = [];
    },
    // Clear validation result
    clearValidationResult: (state) => {
      state.validationResult = null;
    },
    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch payment methods
    builder
      .addCase(fetchPaymentMethods.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPaymentMethods.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentMethods = action.payload;
        state.error = null;
      })
      .addCase(fetchPaymentMethods.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch single payment method
    builder
      .addCase(fetchPaymentMethod.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPaymentMethod.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPaymentMethod = action.payload;
        state.error = null;
      })
      .addCase(fetchPaymentMethod.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Create payment method
    builder
      .addCase(createPaymentMethod.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPaymentMethod.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentMethods.unshift(action.payload);
        state.error = null;
      })
      .addCase(createPaymentMethod.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Update payment method
    builder
      .addCase(updatePaymentMethod.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePaymentMethod.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.paymentMethods.findIndex(
          (method) => method.id === action.payload.id
        );
        if (index !== -1) {
          state.paymentMethods[index] = action.payload;
        }
        if (state.currentPaymentMethod?.id === action.payload.id) {
          state.currentPaymentMethod = action.payload;
        }
        state.error = null;
      })
      .addCase(updatePaymentMethod.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Delete payment method
    builder
      .addCase(deletePaymentMethod.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePaymentMethod.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentMethods = state.paymentMethods.filter(
          (method) => method.id !== action.payload.id
        );
        if (state.currentPaymentMethod?.id === action.payload.id) {
          state.currentPaymentMethod = null;
        }
        state.error = null;
      })
      .addCase(deletePaymentMethod.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Bulk delete payment methods
    builder
      .addCase(bulkDeletePaymentMethods.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(bulkDeletePaymentMethods.fulfilled, (state, action) => {
        state.isLoading = false;
        const deletedIds = action.payload.ids;
        state.paymentMethods = state.paymentMethods.filter(
          (method) => !deletedIds.includes(method.id)
        );
        if (
          state.currentPaymentMethod &&
          deletedIds.includes(state.currentPaymentMethod.id)
        ) {
          state.currentPaymentMethod = null;
        }
        state.error = null;
      })
      .addCase(bulkDeletePaymentMethods.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch payment method statistics
    builder
      .addCase(fetchPaymentMethodStatistics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPaymentMethodStatistics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.statistics = action.payload;
        state.error = null;
      })
      .addCase(fetchPaymentMethodStatistics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Search payment methods
    builder
      .addCase(searchPaymentMethods.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchPaymentMethods.fulfilled, (state, action) => {
        state.isLoading = false;
        state.searchResults = action.payload;
        state.error = null;
      })
      .addCase(searchPaymentMethods.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch payment methods by type
    builder
      .addCase(fetchPaymentMethodsByType.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPaymentMethodsByType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.filteredPaymentMethods = action.payload;
        state.error = null;
      })
      .addCase(fetchPaymentMethodsByType.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Validate payment method data
    builder
      .addCase(validatePaymentMethodData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(validatePaymentMethodData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.validationResult = action.payload;
        state.error = null;
      })
      .addCase(validatePaymentMethodData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.validationResult = {
          isValid: false,
          message: action.payload as string,
        };
      });
  },
});

export const {
  clearError,
  setCurrentPaymentMethod,
  clearCurrentPaymentMethod,
  clearSearchResults,
  clearFilteredPaymentMethods,
  clearValidationResult,
  setLoading,
} = paymentSlice.actions;

export default paymentSlice.reducer;
