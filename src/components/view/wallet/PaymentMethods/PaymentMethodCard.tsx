import React from "react";
import { Trash2, Edit, Shield, Calendar, User } from "lucide-react";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { PaymentMethod } from "./types";
import { getPaymentIcon } from "./utils";

interface PaymentMethodCardProps {
  method: PaymentMethod;
  onSetDefault: (id: string) => void;
  onDelete: (id: string) => void;
  onEdit: (id: string) => void;
}

export const PaymentMethodCard: React.FC<PaymentMethodCardProps> = ({
  method,
  onSetDefault,
  onDelete,
  onEdit,
}) => {
  const formatCardNumber = (last4: string) => `•••• •••• •••• ${last4}`;

  const getDisplayText = () => {
    switch (method.type) {
      case "credit_card":
      case "debit_card":
        return formatCardNumber(method.last4 || "");
      case "paypal":
        return method.email;
      case "bank_account":
        return `${method.bankName} •••• ${method.last4}`;
      case "apple_pay":
        return "Apple Pay";
      case "google_pay":
        return "Google Pay";
      default:
        return method.holderName;
    }
  };

  return (
    <div className="border border-zinc-700 rounded-lg p-4 hover:bg-zinc-700/30 transition-colors">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          {getPaymentIcon(method.provider)}
          <div>
            <div className="flex items-center gap-2">
              <span className="text-white font-medium">{getDisplayText()}</span>
              {method.isDefault && (
                <Badge className="bg-lime-600 text-black text-xs">
                  Default
                </Badge>
              )}
              {method.isVerified && (
                <Shield className="w-4 h-4 text-green-400" />
              )}
            </div>
            <div className="flex items-center gap-2 text-sm text-zinc-400">
              <User className="w-3 h-3" />
              <span>{method.holderName}</span>
              {(method.type === "credit_card" ||
                method.type === "debit_card") &&
                method.expiryMonth &&
                method.expiryYear && (
                  <>
                    <Calendar className="w-3 h-3 ml-2" />
                    <span>
                      {method.expiryMonth}/{method.expiryYear}
                    </span>
                  </>
                )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {!method.isDefault && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSetDefault(method.id)}
              className="text-zinc-400 hover:text-white text-xs"
            >
              Set Default
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(method.id)}
            className="text-zinc-400 hover:text-white"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(method.id)}
            className="text-zinc-400 hover:text-red-400"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="text-xs text-zinc-500">
        Added {new Date(method.createdAt).toLocaleDateString()}
      </div>
    </div>
  );
};
