import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { DocumentService } from "@/lib/api/services/document";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Check if requesting a specific document by ID
    if (!params.id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    const result = await documentService.getDocument(params.id);
    return NextResponse.json(result, { status: result.success ? 200 : 404 });
  } catch (error) {
    console.error("Documents GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Extract request body
    const body = await request.json();

    if (!params.id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Use the service to update document
    const result = await documentService.updateDocument(params.id, body);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Document update error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize document service
    const session: any = await auth();
    const documentService = new DocumentService();

    // Set the service context with session and request
    documentService.setContext({
      session,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        { success: false, error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Use the service to delete document
    const result = await documentService.deleteDocument(params.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Document delete error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
