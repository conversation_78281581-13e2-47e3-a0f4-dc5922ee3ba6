import { useSelector, useDispatch } from "react-redux";
import { useCallback } from "react";
import { AppDispatch } from "@/store";
import { useRB<PERSON> as useRBACContext } from "@/lib/rbac/context";
import { useAuth } from "@/hooks/useAuth";
import use<PERSON><PERSON> from "swr";
import { api, fetcher } from "@/lib/common/requests";
import {
  updateUserProfile,
  assignRoleToUser,
  removeRoleFromUser,
  fetchAvailablePermissions,
  Role,
  Profile,
} from "@/store/actions/rbac";

// Import role actions from separate file
import {
  createRole as createRoleAction,
  updateRole as updateRoleAction,
  deleteRole as deleteRoleAction,
  updateRolePermissions as updateRolePermissionsAction,
} from "@/store/actions/roles";
import {
  selectRBACLoading,
  selectRBACError,
  clearError,
} from "@/store/slices/rbac";
import { EntityType, PermissionAction } from "@/lib/rbac/types";
import { createPermissionId, parsePermissionId } from "@/lib/rbac";

// Using centralized fetcher from @/lib/common/requests

export function useRBAC() {
  // Use the RBAC context instead of direct Redux selectors
  return useRBACContext();
}

// Hook that uses SWR for direct database access
export function useRBACRedux() {
  const dispatch = useDispatch<AppDispatch>();
  const { user: currentUser } = useAuth(); // Get current user from auth state

  // Use SWR for data fetching instead of Redux selectors
  const {
    data: usersData,
    error: usersError,
    mutate: mutateUsers,
  } = useSWR("rbac/users", fetcher);
  const {
    data: rolesData,
    error: rolesError,
    mutate: mutateRoles,
  } = useSWR("roles", fetcher);

  // Only use Redux for common states (loading, error)
  const isLoading = useSelector(selectRBACLoading);
  const reduxError = useSelector(selectRBACError);

  // Extract users and roles from SWR data (API response format)
  const users = usersData?.data?.users || [];
  const roles = rolesData?.data?.roles || [];

  // Combine errors from SWR and Redux
  const error = reduxError || usersError || rolesError;

  // Compute usersWithRoles from SWR data
  const usersWithRoles = users.map((user: any) => ({
    ...user,
    role: user.roleId
      ? roles.find((role: any) => role.id === user.roleId)
      : undefined,
  }));

  // Actions - now use SWR mutate for refreshing data
  const loadUsers = useCallback(() => {
    mutateUsers(); // Refresh SWR data
  }, [mutateUsers]);

  const loadRoles = useCallback(() => {
    mutateRoles(); // Refresh SWR data
  }, [mutateRoles]);

  const loadCurrentUser = useCallback(() => {
    // Current user data comes from auth state, no need to refresh via SWR
    // This function is kept for backward compatibility
  }, []);

  const refreshData = useCallback(() => {
    mutateUsers(); // Refresh users data via SWR
    mutateRoles(); // Refresh roles data via SWR
    // Current user data comes from auth state, no need to refresh via SWR
  }, [mutateUsers, mutateRoles]);

  const initializeRBAC = useCallback(() => {
    refreshData();
  }, [refreshData]);

  const updateProfile = useCallback(
    async (profileData: Partial<Profile>) => {
      const result = await dispatch(updateUserProfile(profileData));
      // Current user data will be updated via auth state
      return result;
    },
    [dispatch]
  );

  const createNewRole = useCallback(
    async (roleData: {
      name: string;
      permissions: Record<string, string[]>;
    }) => {
      const result = await dispatch(createRoleAction(roleData));
      mutateRoles(); // Refresh roles data after creation
      return result;
    },
    [dispatch, mutateRoles]
  );

  const updateExistingRole = useCallback(
    async (roleId: string, roleData: Partial<Role>) => {
      const result = await dispatch(updateRoleAction({ roleId, roleData }));
      mutateRoles(); // Refresh roles data after update
      return result;
    },
    [dispatch, mutateRoles]
  );

  const deleteExistingRole = useCallback(
    async (roleId: string) => {
      const result = await dispatch(deleteRoleAction(roleId));
      mutateRoles(); // Refresh roles data after deletion
      return result;
    },
    [dispatch, mutateRoles]
  );

  const assignRole = useCallback(
    async (userId: string, roleId: string) => {
      const result = await dispatch(assignRoleToUser({ userId, roleId }));
      mutateUsers(); // Refresh users data after role assignment
      return result;
    },
    [dispatch, mutateUsers]
  );

  const removeRole = useCallback(
    async (userId: string) => {
      const result = await dispatch(removeRoleFromUser(userId));
      mutateUsers(); // Refresh users data after role removal
      return result;
    },
    [dispatch, mutateUsers]
  );

  const updatePermissions = useCallback(
    async (roleId: string, permissions: Record<string, string[]>) => {
      const result = await dispatch(
        updateRolePermissionsAction({ roleId, permissions })
      );
      mutateRoles(); // Refresh roles data after permission update
      return result;
    },
    [dispatch, mutateRoles]
  );

  const loadAvailablePermissions = useCallback(() => {
    dispatch(fetchAvailablePermissions());
  }, [dispatch]);

  const clearRBACError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Initialize data on mount - removed duplicate, using the one above

  return {
    // Data
    users,
    roles,
    error,
    isLoading,
    currentUser,
    usersWithRoles,

    // Actions
    loadUsers,
    loadRoles,
    loadCurrentUser,
    updateProfile,
    createNewRole,
    updateExistingRole,
    deleteExistingRole,
    assignRole,
    removeRole,
    updatePermissions,
    loadAvailablePermissions,
    clearRBACError,
    initializeRBAC,
  };
}

// Hook for getting users by role using SWR data
export function useUsersByRole(roleId: string) {
  const { data: usersData } = useSWR("rbac/users", fetcher);
  // Extract users from SWR data (API response format)
  const users = usersData?.users || [];
  return users.filter((user: any) => user.roleId === roleId);
}

// Hook for getting a specific role using SWR data
export function useRole(roleId: string) {
  const {
    data: role,
    error,
    mutate,
  } = useSWR(roleId ? `roles/${roleId}` : null, fetcher);
  return {
    role,
    error,
    isLoading: !role && !error,
    refresh: mutate,
  };
}

// Hook for permission checking using auth state
export function usePermissions() {
  const { user: currentUser } = useAuth();

  // Entity-based permission checking (new format)
  const hasEntityPermission = useCallback(
    (entity: string, action: string): boolean => {
      if (!currentUser?.role?.permissions) return false;
      const entityPermissions = (currentUser.role.permissions as any)[entity];
      return entityPermissions ? entityPermissions.includes(action) : false;
    },
    [currentUser]
  );

  // Legacy string-based permission checking (backward compatibility)
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!currentUser?.role?.permissions) return false;

      // Try to parse as entity:action format
      const parsed = parsePermissionId(permission);
      if (parsed) {
        return hasEntityPermission(parsed.entity, parsed.action);
      }

      // Fallback: check if permissions is still array format (legacy)
      if (Array.isArray(currentUser.role.permissions)) {
        return currentUser.role.permissions.includes(permission);
      }

      return false;
    },
    [currentUser, hasEntityPermission]
  );

  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.some((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  const hasAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.every((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  // Entity-based permission checking for multiple permissions
  const hasAnyEntityPermission = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.some((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  const hasAllEntityPermissions = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.every((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  // Get user permissions in different formats
  const getUserPermissions = useCallback((): any => {
    return currentUser?.role?.permissions || {};
  }, [currentUser]);

  const getUserPermissionsAsArray = useCallback((): string[] => {
    const permissions = currentUser?.role?.permissions;
    if (!permissions) return [];

    // If it's already an array (legacy format), return as is
    if (Array.isArray(permissions)) return permissions;

    // Convert entity-based object to string array
    const permissionArray: string[] = [];
    Object.entries(permissions).forEach(([entity, actions]) => {
      if (Array.isArray(actions)) {
        actions.forEach((action) => {
          permissionArray.push(createPermissionId(entity, action));
        });
      }
    });
    return permissionArray;
  }, [currentUser]);

  return {
    // Legacy functions (backward compatibility)
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getUserPermissions,
    getUserPermissionsAsArray,

    // New entity-based functions
    hasEntityPermission,
    hasAnyEntityPermission,
    hasAllEntityPermissions,

    // User data
    userRole: currentUser?.role,
    currentUser,
  };
}

// Hook for managing user profiles
export function useUserProfile() {
  const dispatch = useDispatch<AppDispatch>();

  const { user: currentUser } = useAuth();
  const isLoading = useSelector(selectRBACLoading);

  const profile = currentUser?.profile; // Get profile from auth state

  const upsertProfile = useCallback(
    async (profileData: Partial<Profile>) => {
      const result: any = await dispatch(updateUserProfile(profileData));
      // Profile data will be updated via auth state
      return result;
    },
    [dispatch]
  );

  return {
    profile,
    currentUser,
    isLoading,
    error: null, // No error from auth state
    updateUserProfile: upsertProfile,
    upsertProfile,
    hasProfile: !!profile,
  };
}

// Hook for role management operations using SWR data
export function useRoleManagement() {
  const dispatch = useDispatch<AppDispatch>();
  const {
    data: rolesData,
    error: rolesError,
    mutate: mutateRoles,
  } = useSWR("roles", fetcher);

  // Extract roles from SWR data (API response format)
  const roles = rolesData?.data.roles || [];

  // Only use Redux for common states (loading, error)
  const isLoading = useSelector(selectRBACLoading);
  const error = useSelector(selectRBACError) || rolesError;

  const createRole = useCallback(
    async (name: string, permissions: Record<string, string[]> = {}) => {
      const result: any = await dispatch(
        createRoleAction({ name, permissions })
      );
      mutateRoles(); // Refresh roles data after creation

      // Extract the actual API response from Redux action result
      if (result.type.endsWith("/fulfilled")) {
        return { success: true, data: result.payload };
      } else if (result.type.endsWith("/rejected")) {
        return {
          success: false,
          error: result.payload || result.error?.message,
        };
      }
      return result;
    },
    [dispatch, mutateRoles]
  );

  const updateRole = useCallback(
    async (id: string, data: Partial<Role>) => {
      const result: any = await dispatch(updateRoleAction({ id, data }));
      mutateRoles(); // Refresh roles data after update

      // Extract the actual API response from Redux action result
      if (result.type.endsWith("/fulfilled")) {
        return { success: true, data: result.payload };
      } else if (result.type.endsWith("/rejected")) {
        return {
          success: false,
          error: result.payload || result.error?.message,
        };
      }
      return result;
    },
    [dispatch, mutateRoles]
  );

  const deleteRole = useCallback(
    async (roleId: string) => {
      const result: any = await dispatch(deleteRoleAction(roleId));
      mutateRoles(); // Refresh roles data after deletion

      // Extract the actual API response from Redux action result
      if (result.type.endsWith("/fulfilled")) {
        return { success: true, data: result.payload };
      } else if (result.type.endsWith("/rejected")) {
        return {
          success: false,
          error: result.payload || result.error?.message,
        };
      }
      return result;
    },
    [dispatch, mutateRoles]
  );

  const updateRolePermissions = useCallback(
    async (roleId: string, permissions: Record<string, string[]>) => {
      const result: any = await dispatch(
        updateRolePermissionsAction({ roleId, permissions })
      );

      // Extract the actual API response from Redux action result
      if (result.type.endsWith("/fulfilled")) {
        return { success: true, data: result.payload };
      } else if (result.type.endsWith("/rejected")) {
        return {
          success: false,
          error: result.payload || result.error?.message,
        };
      }
      return result;
    },
    [dispatch]
  );

  return {
    roles,
    isLoading,
    error,
    createRole,
    updateRole,
    deleteRole,
    updateRolePermissions,
  };
}

// Hook for user management operations using SWR data
export function useUserManagement() {
  const dispatch = useDispatch<AppDispatch>();

  const {
    data: usersData,
    error: usersError,
    mutate: mutateUsers,
  } = useSWR("rbac/users", fetcher);

  const { data: rolesData } = useSWR("roles", fetcher);

  const isLoading = useSelector(selectRBACLoading);
  const error = useSelector(selectRBACError) || usersError;

  // Extract users and roles from SWR data (API response format)
  const users = usersData?.data?.users || [];
  const roles = rolesData?.data?.roles || [];

  // Compute usersWithRoles from SWR data
  const usersWithRoles = users.map((user: any) => ({
    ...user,
    role:
      roles && roles.length > 0 && user.roleId
        ? roles.find((role: any) => role.id === user.roleId)
        : undefined,
  }));

  const assignRole = useCallback(
    async (userId: string, roleId: string) => {
      const result: any = await dispatch(assignRoleToUser({ userId, roleId }));
      mutateUsers(); // Refresh users data after role assignment
      return result;
    },
    [dispatch, mutateUsers]
  );

  const removeRole = useCallback(
    async (userId: string) => {
      const result: any = await dispatch(removeRoleFromUser(userId));
      mutateUsers(); // Refresh users data after role removal
      return result;
    },
    [dispatch, mutateUsers]
  );

  const getUsersWithoutRole = useCallback(() => {
    return users.filter((user: any) => !user.roleId);
  }, [users]);

  const getUsersWithRole = useCallback(
    (roleId: string) => {
      return users.filter((user: any) => user.roleId === roleId);
    },
    [users]
  );

  return {
    users: usersWithRoles, // Return users with role data
    isLoading,
    error,
    assignRoleToUser: assignRole,
    removeRoleFromUser: removeRole,
    getUsersWithoutRole,
    getUsersWithRole,
  };
}

/**
 * Hook for checking multiple permissions at once
 */
export function useMultiplePermissions(
  checks: Array<{
    entity: EntityType;
    action: PermissionAction;
    resourceId?: string;
  }>
) {
  const { hasEntityPermission } = usePermissions();

  const results = checks.map((check) => ({
    ...check,
    granted: hasEntityPermission(check.entity, check.action),
  }));

  const allGranted = results.every((result) => result.granted);
  const anyGranted = results.some((result) => result.granted);

  return {
    results,
    allGranted,
    anyGranted,
  };
}

/**
 * Hook for entity-specific permissions (all CRUD operations)
 */
export function useEntityPermissions(entity: EntityType, resourceId?: string) {
  const { hasEntityPermission } = usePermissions();

  return {
    canCreate: hasEntityPermission(entity, "create"),
    canRead: hasEntityPermission(entity, "read"),
    canUpdate: hasEntityPermission(entity, "update"),
    canDelete: hasEntityPermission(entity, "delete"),

    // Convenience methods
    hasFullAccess:
      hasEntityPermission(entity, "create") &&
      hasEntityPermission(entity, "read") &&
      hasEntityPermission(entity, "update") &&
      hasEntityPermission(entity, "delete"),
    hasReadOnly:
      hasEntityPermission(entity, "read") &&
      !hasEntityPermission(entity, "create") &&
      !hasEntityPermission(entity, "update") &&
      !hasEntityPermission(entity, "delete"),
  };
}
