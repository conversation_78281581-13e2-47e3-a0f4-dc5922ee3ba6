import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Session } from "next-auth";
import { PaymentMethod } from "@/components/view/wallet/PaymentMethods/types";
import { updateUserAvatar } from "../actions/auth";
import { updateUserProfile } from "../actions/rbac";
import { createPaymentMethod, updatePaymentMethod } from "../actions/payment";

export interface User extends Session {
  user: Session["user"] & {
    customerId: string;
    role: Role;
    profile: Profile;
    account: { id: string };
    payment_methods: PaymentMethod[];
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface Profile {
  id: string;
  about: string;
  website: string;
  socials: string[];
  location: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  status: string;
  permissions: string[];
}

// Define the auth state interface
export interface AuthState {
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: any;
  isDashboardView: boolean;
  user: {
    id?: string;
    name?: string | null;
    customerId: string;
    email?: string | null;
    image?: string | null;
    role: Role;
    profile: Profile;
    account: { id: string };
    createdAt: Date;
    updatedAt: Date;
    payment_methods: PaymentMethod[];
  } | null;
}

// Initial state
const initialState: AuthState = {
  session: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  isDashboardView: false,
  user: {
    id: "",
    name: "",
    email: "",
    image: "",
    customerId: "",
    role: {
      id: "",
      name: "",
      description: "",
      status: "",
      permissions: [],
    },
    profile: {
      id: "",
      about: "",
      website: "",
      socials: [],
      location: "",
    },
    account: { id: "" },
    payment_methods: [],
  },
};

// Create the auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Set session data from next-auth
    setSession: (state, action: PayloadAction<User>) => {
      state.session = action.payload;
      state.isAuthenticated = !!action.payload;
      state.isLoading = false;

      if (action.payload?.user) {
        state.user = {
          id: action.payload.user.id,
          name: action.payload.user.name,
          email: action.payload.user.email,
          image: action.payload.user.image,
          role: action.payload.user.role,
          customerId: action.payload.user.customerId,
          createdAt: action.payload.user.createdAt,
          updatedAt: action.payload.user.updatedAt,
          profile: action.payload.user.profile,
          account: action.payload.user.account,
          payment_methods: action.payload.user.payment_methods,
        };
      } else {
        state.user = null;
      }
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Clear session (logout)
    clearSession: (state) => {
      state.session = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.user = null;
    },

    // Update user profile data
    updateUser: (state, action: PayloadAction<Partial<AuthState["user"]>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },

    // Set dashboard view state
    setDashboardView: (state, action: PayloadAction<boolean>) => {
      state.isDashboardView = action.payload;
    },

    // Toggle dashboard view state
    toggleDashboardView: (state) => {
      state.isDashboardView = !state.isDashboardView;
    },
  },
  extraReducers: (builder) => {
    // Upsert user profile (create or update)
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user) {
          state.user.profile = action.payload;
        }
      })
      .addCase(updateUserProfile.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(updateUserAvatar.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserAvatar.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user) {
          state.user.image = action.payload;
        }
      })
      .addCase(updateUserAvatar.rejected, (state) => {
        state.isLoading = false;
      });

    builder
      .addCase(createPaymentMethod.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPaymentMethod.fulfilled, (state, action) => {
        state.error = null;
        state.isLoading = false;

        if (state.user?.payment_methods) {
          state.user?.payment_methods.push(action.payload);
        }
      })
      .addCase(createPaymentMethod.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  setSession,
  setLoading,
  clearSession,
  updateUser,
  setDashboardView,
  toggleDashboardView,
} = authSlice.actions;

// Export selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectSessionAccount = (state: { auth: AuthState }) =>
  state.auth.session?.user?.account;
export const selectPaymentMethods = (state: { auth: AuthState }) =>
  state.auth.user?.payment_methods;
export const selectPermissions = (state: { auth: AuthState }) =>
  state.auth.user?.role?.permissions;
export const selectUserAccount = (state: { auth: AuthState }) =>
  state.auth?.user?.account;
export const selectProfile = (state: { auth: AuthState }) =>
  state.auth.user?.profile;
export const selectPersonlizedRoute = (state: { auth: AuthState }) =>
  state.auth.user?.name?.trim().replace(" ", "-").toLowerCase();
export const selectSession = (state: { auth: AuthState }) => state.auth.session;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) =>
  state.auth.isLoading;
export const selectIsDashboardView = (state: { auth: AuthState }) =>
  state.auth.isDashboardView;

// Export reducer
export default authSlice;
