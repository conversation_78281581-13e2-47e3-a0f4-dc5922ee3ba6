"use client";

import React from "react";
import { Card, CardContent } from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { mockScheduleEvents, mockWorkingFormat } from "@/data/dashboard-mock";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/common/ui/chart";
import { Bar, BarChart } from "recharts";
import { Calendar } from "@/components/common/ui/calendar";
import { QuickStats } from "./quickstats";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/common/ui/table";
import { useDashboard } from "@/hooks/useDashboard";

// Types for better type safety
interface WorkingFormatBarProps {
  title: string;
  percentage: number;
  color: string;
}

// Chart configuration
const chartConfig = {
  value: {
    label: "Percentage",
    color: "#84cc16",
  },
};

// Working Format Bar Component
const WorkingFormatBar = ({
  title,
  percentage,
  color,
}: WorkingFormatBarProps) => {
  const chartData = [{ name: title, value: percentage }];

  return (
    <div className="w-full relative">
      <div className="bottom-[15%] left-[25%] -translate-x-[25%] absolute flex flex-col items-start z-10">
        <span className="text-sm text-white capitalize">{title}</span>
        <span className="text-xl font-bold text-white">{percentage}%</span>
      </div>
      <ChartContainer config={chartConfig} className="h-full w-full">
        <BarChart data={chartData} layout="horizontal">
          <Bar
            dataKey="value"
            fill={color}
            radius={[12, 12, 0, 0]}
            maxBarSize={800}
          />
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent hideLabel />}
          />
        </BarChart>
      </ChartContainer>
    </div>
  );
};

// Working Format Header Component
const WorkingFormatHeader = () => {
  return (
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-sm font-medium text-white">Working format</h3>
      <span className="text-xs text-zinc-400">Details</span>
    </div>
  );
};

// Working Format Charts Component
const WorkingFormatCharts = () => {
  return (
    <div className="flex flex-row items-end justify-between">
      <WorkingFormatBar
        title="hybrid"
        percentage={mockWorkingFormat.hybrid}
        color="#84cc16"
      />
      <WorkingFormatBar
        title="remote"
        percentage={mockWorkingFormat.remote}
        color="#52525b"
      />
    </div>
  );
};

const UpcomingSchedules = () => {
  return (
    <Card className="w-full h-full bg-zinc-800 border-zinc-700">
      <CardContent className="flex flex-col gap-8">
        <h3 className="text-sm font-medium text-white">Upcoming</h3>
        <div className="flex flex-col gap-3">
          {mockScheduleEvents.map((event: any, index: number) => {
            return (
              <div
                key={index}
                className="bg-zinc-700 p-4 rounded-lg border-l-4 border-lime-300 flex items-center justify-between"
              >
                <span className="text-sm text-white">{event.title}</span>
                <span className="text-xs text-zinc-400">{event.time}</span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// Working Format Card Component
const WorkingFormatCard = () => {
  return (
    <Card className="w-full bg-zinc-800 border-zinc-700">
      <CardContent className="p-6">
        <WorkingFormatHeader />
        <WorkingFormatCharts />
      </CardContent>
    </Card>
  );
};

// Calendar Card Component
const CalendarCard = () => {
  return (
    <Card className="w-full bg-zinc-800 border-zinc-700">
      <CardContent className="p-6">
        <Calendar
          mode="single"
          selected={new Date()}
          onSelect={(date) => console.log(date)}
          buttonVariant="outline"
        />
      </CardContent>
    </Card>
  );
};

// Dashboard Summary Cards Component
const DashboardSummaryCards = () => {
  const { summary, contractStatistics, proposalStatistics, isLoading } =
    useDashboard();

  return (
    <div className="grid grid-cols-2 gap-4 mb-6">
      {/* Contracts Summary Card */}
      <Card className="bg-zinc-800 border-zinc-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-white">Contracts</h3>
            <div className="w-8 h-8 bg-lime-500/20 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-lime-500 rounded"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-white">
              {isLoading ? "..." : summary?.contractsCount || 0}
            </div>
            <div className="text-sm text-zinc-400">Total contracts</div>
            {contractStatistics && (
              <div className="flex items-center gap-4 text-xs text-zinc-500">
                <span>Active: {contractStatistics.active || 0}</span>
                <span>Completed: {contractStatistics.completed || 0}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Proposals Summary Card */}
      <Card className="bg-zinc-800 border-zinc-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-white">Proposals</h3>
            <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-white">
              {isLoading ? "..." : summary?.proposalsCount || 0}
            </div>
            <div className="text-sm text-zinc-400">Total proposals</div>
            {proposalStatistics && (
              <div className="flex items-center gap-4 text-xs text-zinc-500">
                <span>Pending: {proposalStatistics.pending || 0}</span>
                <span>Approved: {proposalStatistics.approved || 0}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Recent Contracts Table Component
const RecentContractsTable = () => {
  const { recentContracts, isLoadingContracts } = useDashboard();

  if (isLoadingContracts) {
    return (
      <div className="text-center text-zinc-400 py-8">
        Loading recent contracts...
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow className="border-zinc-700">
          <TableHead className="text-zinc-400">Contract</TableHead>
          <TableHead className="text-zinc-400">Status</TableHead>
          <TableHead className="text-zinc-400">Value</TableHead>
          <TableHead className="text-zinc-400">Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {recentContracts.length === 0 ? (
          <TableRow>
            <TableCell colSpan={4} className="text-center text-zinc-500 py-8">
              No recent contracts found
            </TableCell>
          </TableRow>
        ) : (
          recentContracts.map((contract: any) => (
            <TableRow
              key={contract.id}
              className="border-zinc-700 hover:bg-zinc-700/50"
            >
              <TableCell className="text-white">
                <div>
                  <div className="font-medium">
                    {contract.title || contract.name}
                  </div>
                  <div className="text-sm text-zinc-400 truncate max-w-[200px]">
                    {contract.description}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    contract.status === "active"
                      ? "bg-lime-500/20 text-lime-400"
                      : contract.status === "completed"
                      ? "bg-green-500/20 text-green-400"
                      : contract.status === "draft"
                      ? "bg-yellow-500/20 text-yellow-400"
                      : "bg-zinc-500/20 text-zinc-400"
                  }`}
                >
                  {contract.status}
                </span>
              </TableCell>
              <TableCell className="text-white">
                ${contract.contract_value?.toLocaleString() || "0"}
              </TableCell>
              <TableCell className="text-zinc-400">
                {new Date(contract.createdAt).toLocaleDateString()}
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
};

// Recent Proposals Table Component
const RecentProposalsTable = () => {
  const { recentProposals, isLoadingProposals } = useDashboard();

  if (isLoadingProposals) {
    return (
      <div className="text-center text-zinc-400 py-8">
        Loading recent proposals...
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow className="border-zinc-700">
          <TableHead className="text-zinc-400">Proposal</TableHead>
          <TableHead className="text-zinc-400">Status</TableHead>
          <TableHead className="text-zinc-400">Budget</TableHead>
          <TableHead className="text-zinc-400">Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {recentProposals.length === 0 ? (
          <TableRow>
            <TableCell colSpan={4} className="text-center text-zinc-500 py-8">
              No recent proposals found
            </TableCell>
          </TableRow>
        ) : (
          recentProposals.map((proposal: any) => (
            <TableRow
              key={proposal.id}
              className="border-zinc-700 hover:bg-zinc-700/50"
            >
              <TableCell className="text-white">
                <div>
                  <div className="font-medium">
                    {proposal.name || proposal.title}
                  </div>
                  <div className="text-sm text-zinc-400 truncate max-w-[200px]">
                    {proposal.description}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    proposal.status === "agreed" ||
                    proposal.status === "inprogress"
                      ? "bg-lime-500/20 text-lime-400"
                      : proposal.status === "completed"
                      ? "bg-green-500/20 text-green-400"
                      : proposal.status === "created" ||
                        proposal.status === "submitted"
                      ? "bg-blue-500/20 text-blue-400"
                      : proposal.status === "negotiating" ||
                        proposal.status === "reviewing"
                      ? "bg-yellow-500/20 text-yellow-400"
                      : proposal.status === "rejected"
                      ? "bg-red-500/20 text-red-400"
                      : "bg-zinc-500/20 text-zinc-400"
                  }`}
                >
                  {proposal.status}
                </span>
              </TableCell>
              <TableCell className="text-white">
                $
                {proposal.total_budget?.toLocaleString() ||
                  proposal.fixed_budget?.toLocaleString() ||
                  "0"}
              </TableCell>
              <TableCell className="text-zinc-400">
                {new Date(proposal.createdAt).toLocaleDateString()}
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
};

const ProjectAndSummary = () => {
  return (
    <div className="space-y-6">
      <DashboardSummaryCards />

      {/* Recent Items Tabbed Section */}
      <Card className="bg-zinc-800 border-zinc-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">
              Recent Activity
            </h3>
            <Button
              variant="ghost"
              size="sm"
              className="text-zinc-400 hover:text-white"
            >
              View All
            </Button>
          </div>

          <Tabs defaultValue="contracts" className="w-full">
            <TabsList className="bg-zinc-700 border-zinc-600 mb-6">
              <TabsTrigger
                value="contracts"
                className="data-[state=active]:bg-lime-500 data-[state=active]:text-black"
              >
                Recent Contracts
              </TabsTrigger>
              <TabsTrigger
                value="proposals"
                className="data-[state=active]:bg-lime-500 data-[state=active]:text-black"
              >
                Recent Proposals
              </TabsTrigger>
            </TabsList>

            <TabsContent value="contracts" className="mt-0">
              <RecentContractsTable />
            </TabsContent>

            <TabsContent value="proposals" className="mt-0">
              <RecentProposalsTable />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

const SummaryReview = () => {
  return (
    <Tabs defaultValue="summary" className="flex flex-col gap-6">
      <TabsList className="bg-zinc-800 border-zinc-700">
        <TabsTrigger
          value="summary"
          className="data-[state=active]:bg-lime-500 data-[state=active]:text-black"
        >
          Summary
        </TabsTrigger>
        <TabsTrigger
          value="schedule"
          className="data-[state=active]:bg-lime-500 data-[state=active]:text-black"
        >
          Schedule
        </TabsTrigger>
      </TabsList>
      <TabsContent value="summary">
        <ProjectAndSummary />
      </TabsContent>
      <TabsContent value="schedule" className="flex flex-col gap-6">
        <span className="flex flex-col items-start gap-6">
          <WorkingFormatCard />
          <UpcomingSchedules />
        </span>
        <CalendarCard />
      </TabsContent>
    </Tabs>
  );
};

// Main Schedules Component using compound pattern
export const Summary = () => {
  return (
    <div className="flex flex-col gap-6">
      <QuickStats />
      <SummaryReview />
    </div>
  );
};
