"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ContractService } from "@/lib/api/services/contract";

// GET /api/contract/[id] - Get single contract
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    let { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Contract ID is required" },
        { status: 400 }
      );
    }

    // Use the service to get contract
    const result = await contractService.getContract(id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Contract GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PATCH /api/contract/[id] - Update contract
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        { success: false, error: "Contract ID is required" },
        { status: 400 }
      );
    }
    // Parse request body
    const updateData = await request.json();

    // Use the service to update contract
    const result = await contractService.updateContract(params.id, updateData);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Contract PATCH error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/contract/[id] - Delete contract
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Initialize contract service
    const session: any = await auth();
    const contractService = new ContractService();

    // Set the service context with session and request
    contractService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    if (!params.id) {
      return NextResponse.json(
        { success: false, error: "Contract ID is required" },
        { status: 400 }
      );
    }

    // Use the service to delete contract
    const result = await contractService.deleteContract(params.id);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Contract DELETE error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
