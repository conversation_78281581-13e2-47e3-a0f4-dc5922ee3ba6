import { CreditCard } from "lucide-react";

// Payment method icons
export const getPaymentIcon = (provider: string) => {
  const iconClass = "w-8 h-8";

  switch (provider) {
    case "visa":
      return (
        <div
          className={`${iconClass} bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          VISA
        </div>
      );
    case "mastercard":
      return (
        <div
          className={`${iconClass} bg-red-600 rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          MC
        </div>
      );
    case "amex":
      return (
        <div
          className={`${iconClass} bg-green-600 rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          AMEX
        </div>
      );
    case "discover":
      return (
        <div
          className={`${iconClass} bg-orange-600 rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          DISC
        </div>
      );
    case "paypal":
      return (
        <div
          className={`${iconClass} bg-blue-500 rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          PP
        </div>
      );
    case "apple":
      return (
        <div
          className={`${iconClass} bg-black rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          🍎
        </div>
      );
    case "google":
      return (
        <div
          className={`${iconClass} bg-gray-600 rounded flex items-center justify-center text-white text-xs font-bold`}
        >
          G
        </div>
      );
    default:
      return <CreditCard className={iconClass} />;
  }
};

// Helper function to map payment types
export const mapPaymentType = (
  type: string
):
  | "visa"
  | "mastercard"
  | "amex"
  | "discover"
  | "paypal"
  | "bank"
  | "apple"
  | "google" => {
  switch (type) {
    case "credit_card":
    case "debit_card":
      return "visa"; // Default to visa for card types
    case "bank_account":
      return "bank";
    case "apple_pay":
      return "apple";
    case "google_pay":
      return "google";
    default:
      return type as
        | "visa"
        | "mastercard"
        | "amex"
        | "discover"
        | "paypal"
        | "bank"
        | "apple"
        | "google";
  }
};
