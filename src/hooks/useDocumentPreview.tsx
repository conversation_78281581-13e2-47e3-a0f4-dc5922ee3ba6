"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

const DocViewer = dynamic(() => import("react-doc-viewer"), {
  ssr: false,
});
import { <PERSON><PERSON><PERSON><PERSON>, DocViewerR<PERSON>ers, IDocument } from "react-doc-viewer";
import { useDocuments } from "@/hooks/useDocuments";
import { Loader2, AlertCircle } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/common/ui/dialog";
import { Button } from "@/components/common/ui/button";

export interface DocumentPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  document: IDocument;
  title?: string;
  onDownload?: () => void;
  onPrint?: () => void;
  className?: string;
}

const CustomPDFRender: DocRenderer = ({ mainState: { currentDocument } }) => {
  if (!currentDocument) return null;

  return (
    <iframe
      id="document-pdf-file"
      width="100%"
      height="100%"
      name="banner"
      title="Document Preview"
      style={{ border: 0, appearance: "none" }}
      src={currentDocument.fileData as string}
    >
      Your browser does not support iframes.
    </iframe>
  );
};

CustomPDFRender.fileTypes = ["pdf", "application/pdf"];
CustomPDFRender.weight = 1;

export function DocumentPreviewDialog({
  isOpen,
  onClose,
  document,
  title,
  onDownload,
  onPrint,
  className = "",
}: DocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setError(null);
      setIsFullscreen(false);
    }
  }, [isOpen, document]);

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    }
  };

  // Prepare document for viewer
  const docs: IDocument[] = document
    ? [
        {
          uri: document.uri,
          fileType: document.fileType || "application/pdf",
        },
      ]
    : [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={`overflow-hidden p-0 ${
          isFullscreen
            ? "w-screen h-screen max-w-none rounded-none"
            : "w-full sm:max-w-4xl h-[90vh]"
        } ${className}`}
      >
        <DialogHeader className="bg-white">
          <DialogTitle></DialogTitle>
        </DialogHeader>

        {/* Document Viewer */}
        <div className="flex-1 relative overflow-hidden">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="flex flex-col items-center gap-3">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                <p className="text-sm text-gray-600">Loading document...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="flex flex-col items-center gap-3 text-center max-w-md">
                <AlertCircle className="h-12 w-12 text-red-500" />
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Unable to Preview Document
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">{error}</p>
                  <Button
                    onClick={handleDownload}
                    className="bg-blue-600 text-white hover:bg-blue-700"
                  >
                    Download Document
                  </Button>
                </div>
              </div>
            </div>
          )}

          {!error && document && (
            <DocViewer
              documents={docs}
              pluginRenderers={[CustomPDFRender, ...DocViewerRenderers]}
              config={{
                header: {
                  disableHeader: true,
                },
              }}
              style={{
                height: "100%",
                width: "100%",
                border: 0,
                appearance: "none",
              }}
            />
          )}
        </div>

        {/* Footer */}
        <DialogFooter className="bg-white"></DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useDocumentPreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [document, setDocument] = useState<
    DocumentPreviewProps["document"] | null
  >(null);

  const { fetchSignedURL } = useDocuments();

  const openPreview = async (
    doc: NonNullable<DocumentPreviewProps["document"]>
  ) => {
    if (!doc) return;
    doc.uri = await fetchSignedURL(doc.uri);
    setDocument(doc);
    setIsOpen(true);
  };

  const closePreview = () => {
    setIsOpen(false);
    setDocument(null);
  };

  return {
    isOpen,
    document,
    openPreview,
    closePreview,
  };
}
