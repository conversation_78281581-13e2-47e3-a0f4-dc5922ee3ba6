"use client";

import React, { Fragment, useState, useEffect } from "react";
import { Plus } from "lucide-react";
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from "@/components/common/ui/avatar";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/common/ui/button";
import { Card, CardContent } from "@/components/common/ui/card";
import { mockUserProfile } from "@/data/dashboard-mock";

// Types for better type safety
interface ProfileInfoItemProps {
  label: string;
  value: string;
  className?: string;
}

interface StatItemProps {
  value: string | number;
  label: string;
  description: string;
  className?: string;
}

// Profile Avatar Component
const ProfileAvatar = () => {
  const { user } = useAuth();

  return (
    <Avatar className="w-64 h-64 mb-4">
      <AvatarImage src={user?.image || ""} alt={user?.name || ""} />
      <AvatarFallback className="text-2xl bg-zinc-600">
        {user?.name
          .split(" ")
          .map((n) => n[0])
          .join("")}
      </AvatarFallback>
    </Avatar>
  );
};

// Profile Info Item Component
const ProfileInfoItem = ({
  label,
  value,
  className = "",
}: ProfileInfoItemProps) => {
  return (
    <div className="mb-4">
      <div className="text-sm text-zinc-400 mb-1">{label}</div>
      <div className={`text-white ${className}`}>{value}</div>
    </div>
  );
};

// Profile Basic Info Component
const ProfileBasicInfo = () => {
  const { user } = useAuth();

  let joinedOn = new Date(user?.createdAt).toLocaleDateString();

  return (
    <div className="mb-6">
      <ProfileAvatar />
      <ProfileInfoItem
        label="Name"
        value={user?.name || "Your name"}
        className="text-xl font-semibold"
      />
      <ProfileInfoItem
        label="Email"
        value={user?.email || "Your email"}
        className="text-lime-400"
      />
      <ProfileInfoItem label="Joined on" value={joinedOn} />
    </div>
  );
};

// Stat Item Component
const StatItem = ({
  value,
  label,
  description,
  className = "",
}: StatItemProps) => {
  return (
    <div className={className}>
      <div className="text-3xl font-bold text-white">
        {typeof value === "number" && label === "Salary"
          ? `$${value.toLocaleString()}`
          : value}
      </div>
      <div className="text-sm text-zinc-400">{label}</div>
      <div className="text-xs text-zinc-500 flex items-center gap-1 mt-1">
        <div className="w-3 h-3 rounded-full border border-zinc-500"></div>
        {description}
      </div>
    </div>
  );
};

// Profile Stats Component
const ProfileStats = () => {
  const { user } = useAuth();
  const [difference, setDifference] = useState({ factor: "years", value: 0 });

  useEffect(() => {
    const today = new Date();
    const joinedDate = new Date(user?.createdAt);

    const diffInYears: number = today.getFullYear() - joinedDate.getFullYear();
    const diffInMonths: number = today.getMonth() - joinedDate.getMonth();
    const diffInDays: number = today.getDay() - joinedDate.getDay();

    if (diffInYears > 0) {
      setDifference({ factor: "years", value: diffInYears });
    }

    if (diffInMonths > 0) {
      setDifference({ factor: "months", value: diffInMonths });
    }

    if (diffInDays > 0) {
      setDifference({ factor: "days", value: diffInDays });
    }
  }, [user]);

  return (
    <div className="flex flex-col gap-8">
      <StatItem
        value={difference.value}
        label={difference.factor}
        description="In company"
      />
      {/* First Row Stats */}
      <div className="flex flex-col gap-3">
        <div className="flex flex-row items-center justify-between">
          <h6>Spent</h6>
        </div>
        <div className="grid grid-cols-2 gap-8 bg-zinc-700 p-6 rounded-xl">
          <StatItem
            value={mockUserProfile.salary}
            label="Available"
            description="Wallet Amount"
          />
        </div>
      </div>
    </div>
  );
};

// Main Profile Component using compound pattern
export const Profile = () => {
  return (
    <Card className="bg-zinc-800 border-zinc-700">
      <CardContent className="p-6">
        <ProfileBasicInfo />
        <ProfileStats />
      </CardContent>
    </Card>
  );
};

// Export sub-components for potential reuse
Profile.Avatar = ProfileAvatar;
Profile.BasicInfo = ProfileBasicInfo;
Profile.InfoItem = ProfileInfoItem;
Profile.Stats = ProfileStats;
Profile.StatItem = StatItem;
