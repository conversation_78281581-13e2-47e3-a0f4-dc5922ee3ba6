"use client";

import React, { use, Suspense, Usable } from "react";
import { ContractDetailsContainer } from "@/components/view/contracts/details/container";

interface ContractDetailsPageProps {
  id: string;
  slug: string;
}

export default function ContractDetailsPage({
  params,
}: {
  params: Usable<ContractDetailsPageProps>;
}) {
  let param: ContractDetailsPageProps = use(params);

  return (
    <Suspense fallback={<div>Loading contract details...</div>}>
      <ContractDetailsContainer contractId={param.id} />
    </Suspense>
  );
}
