import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";

// Types based on the actual schema
export interface User {
  id: string;
  name?: string;
  email: string;
  emailVerified?: Date;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
  roleId?: string;
  role?: Role;
  profile?: Profile[];
}

export interface Profile {
  id: string;
  about: string;
  website: string;
  socials: string[];
  location: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Role {
  id: string;
  name?: string;
  description?: string;
  status?: "active" | "inactive" | "created";
  permissions: Record<string, string[]>; // New JSON-based structure: { [entity]: [actions] }
  createdAt: Date;
  updatedAt?: Date;
}

export interface RBACState {
  users: User[];
  currentUser?: User;
  isLoading: boolean;
  error?: string;
}

// Fetch all users
export const fetchUsers = createAsyncThunk(
  "rbac/fetchUsers",
  async (_, { rejectWithValue }) => {
    try {
      const result = await api.get("rbac/users");
      if (result.success) {
        return result.data;
      }
      return rejectWithValue(result.message || "Failed to fetch users");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch users";
      return rejectWithValue(errorMessage);
    }
  }
);

// Note: Role CRUD operations moved to separate role actions
// RBAC focuses on user-role associations only

// Note: fetchCurrentUser removed - current user data now comes from auth state

// Upsert user profile (create or update)
export const updateUserProfile = createAsyncThunk(
  "rbac/upsertUserProfile",
  async (profileData: Partial<Profile>, { rejectWithValue }) => {
    try {
      // Use POST for upsert operation (server handles create or update logic)
      const result = await api.post("rbac/profile", profileData);
      if (result.success) {
        toast.success("Profile saved successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to save profile");
      return rejectWithValue(result.message || "Failed to save profile");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to save profile";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Role CRUD operations moved to separate role actions file

// Assign role to user
export const assignRoleToUser = createAsyncThunk(
  "rbac/assignRoleToUser",
  async (
    { userId, roleId }: { userId: string; roleId: string },
    { rejectWithValue }
  ) => {
    try {
      const result = await api.post("rbac/assign-role", { userId, roleId });
      if (result.success) {
        toast.success("Role assigned successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to assign role");
      return rejectWithValue(result.message || "Failed to assign role");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to assign role";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Remove role from user
export const removeRoleFromUser = createAsyncThunk(
  "rbac/removeRoleFromUser",
  async (userId: string, { rejectWithValue }) => {
    try {
      const result = await api.post("rbac/remove-role", { userId });
      if (result.success) {
        toast.success("Role removed successfully");
        return result.data;
      }
      toast.error(result.message || "Failed to remove role");
      return rejectWithValue(result.message || "Failed to remove role");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to remove role";
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Role permissions management moved to separate role actions file

// Get available permissions
export const fetchAvailablePermissions = createAsyncThunk(
  "rbac/fetchAvailablePermissions",
  async (_, { rejectWithValue }) => {
    try {
      const result = await api.get("rbac/permissions");
      if (result.success) {
        return result.data;
      }
      return rejectWithValue(result.message || "Failed to fetch permissions");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch permissions";
      return rejectWithValue(errorMessage);
    }
  }
);
