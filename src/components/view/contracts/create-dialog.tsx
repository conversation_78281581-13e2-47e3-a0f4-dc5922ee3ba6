"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/common/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@/components/common/ui/combobox";
import { Textarea } from "@/components/common/ui/textarea";
import { Upload, X, FileText, Calendar, DollarSign } from "lucide-react";
import { toast } from "sonner";
import useSWR from "swr";
import { fetcher } from "@/lib/common/requests";
import type { CreateContract } from "@/lib/api/validators/schemas/contract";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ContractCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateContract: (data: CreateContract) => Promise<void>;
  isLoading: boolean;
}

interface Client {
  id: string;
  email: string;
  name?: string;
}

export function ContractCreateDialog({
  open,
  onOpenChange,
  onCreateContract,
  isLoading,
}: ContractCreateDialogProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    status: "draft" as const,
    client_id: "",
    total_value: 0,
    paid_value: 0,
    remaining_value: 0,
    start_date: "",
    end_date: "",
    proposal_id: "",
  });

  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [clientComboboxOpen, setClientComboboxOpen] = useState(false);
  const [proposalComboboxOpen, setProposalComboboxOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(
    null
  );

  // Fetch clients data
  const {
    data: clientsData,
    error: clientsError,
    isLoading: isLoadingClients,
  } = useSWR("clients", fetcher);

  // Fetch proposals data
  const {
    data: proposalsData,
    error: proposalsError,
    isLoading: isLoadingProposals,
  } = useSWR("proposal", fetcher);

  const clients: Client[] = clientsData?.data || [];
  const proposals: Proposal[] = proposalsData?.data?.proposals || [];

  // Update contract value when proposal is selected
  useEffect(() => {
    if (selectedProposal) {
      const totalValue = selectedProposal.total_budget || 0;
      setFormData((prev) => ({
        ...prev,
        total_value: totalValue,
        remaining_value: totalValue, // Initially, all value is remaining
        paid_value: 0, // Initially, nothing is paid
        proposal_id: selectedProposal.id,
      }));
    }
  }, [selectedProposal]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles((prev) => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!formData.title.trim()) {
      toast.error("Contract title is required");
      return;
    }

    if (!selectedClient) {
      toast.error("Please select a client");
      return;
    }

    if (!formData.start_date || !formData.end_date) {
      toast.error("Start date and end date are required");
      return;
    }

    if (new Date(formData.start_date) >= new Date(formData.end_date)) {
      toast.error("End date must be after start date");
      return;
    }

    try {
      const contractData: CreateContract = {
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        status: formData.status,
        client_id: selectedClient.id,
        total_value: formData.total_value,
        paid_value: formData.paid_value,
        remaining_value: formData.remaining_value,
        start_date: new Date(formData.start_date).toISOString(),
        end_date: new Date(formData.end_date).toISOString(),
        proposal_id: formData.proposal_id || undefined,
      };

      await onCreateContract(contractData);

      // Reset form
      setFormData({
        title: "",
        description: "",
        status: "draft",
        client_id: "",
        total_value: 0,
        paid_value: 0,
        remaining_value: 0,
        start_date: "",
        end_date: "",
        proposal_id: "",
      });
      setSelectedFiles([]);
      setSelectedClient(null);
      setSelectedProposal(null);
      onOpenChange(false);
      toast.success("Contract created successfully");
    } catch (error) {
      console.error("Failed to create contract:", error);
      toast.error("Failed to create contract");
    }
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setFormData((prev) => ({
      ...prev,
      client_id: client.email,
    }));
    setClientComboboxOpen(false);
  };

  const handleProposalSelect = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setProposalComboboxOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Create New Contract
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Contract Title */}
          <div className="space-y-2">
            <Label htmlFor="title">
              Contract Title <span className="text-red-500">*</span>
            </Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              placeholder="Enter contract title"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Enter contract description"
              rows={3}
            />
          </div>

          {/* Client Selection */}
          <div className="space-y-2">
            <Label>
              Client <span className="text-red-500">*</span>
            </Label>
            <Combobox
              open={clientComboboxOpen}
              onOpenChange={setClientComboboxOpen}
            >
              <ComboboxTrigger className="w-full">
                <ComboboxInput
                  placeholder={
                    isLoadingClients
                      ? "Loading clients..."
                      : selectedClient
                      ? selectedClient.email
                      : "Select a client"
                  }
                  className="w-full"
                  readOnly
                />
              </ComboboxTrigger>
              <ComboboxContent>
                <ComboboxCommand>
                  <ComboboxList>
                    <ComboboxEmpty>
                      {clientsError
                        ? "Error loading clients"
                        : "No clients found"}
                    </ComboboxEmpty>
                    <ComboboxGroup>
                      {clients.map((client) => (
                        <ComboboxItem
                          key={client.id}
                          onSelect={() => handleClientSelect(client)}
                        >
                          <ComboboxItemIndicator />
                          <div className="flex flex-col">
                            <span>{client.email}</span>
                            {client.name && (
                              <span className="text-sm text-gray-500">
                                {client.name}
                              </span>
                            )}
                          </div>
                        </ComboboxItem>
                      ))}
                    </ComboboxGroup>
                  </ComboboxList>
                </ComboboxCommand>
              </ComboboxContent>
            </Combobox>
          </div>

          {/* Proposal Selection */}
          <div className="space-y-2">
            <Label>Proposal (Optional)</Label>
            <Combobox
              open={proposalComboboxOpen}
              onOpenChange={setProposalComboboxOpen}
            >
              <ComboboxTrigger className="w-full">
                <ComboboxInput
                  placeholder={
                    isLoadingProposals
                      ? "Loading proposals..."
                      : selectedProposal
                      ? selectedProposal.name
                      : "Select a proposal"
                  }
                  className="w-full"
                  readOnly
                />
              </ComboboxTrigger>
              <ComboboxContent>
                <ComboboxCommand>
                  <ComboboxList>
                    <ComboboxEmpty>
                      {proposalsError
                        ? "Error loading proposals"
                        : "No proposals found"}
                    </ComboboxEmpty>
                    <ComboboxGroup>
                      {proposals.map((proposal) => (
                        <ComboboxItem
                          key={proposal.id}
                          onSelect={() => handleProposalSelect(proposal)}
                        >
                          <ComboboxItemIndicator />
                          <div className="flex flex-col">
                            <span>{proposal.name}</span>
                            <span className="text-sm text-gray-500">
                              ${proposal.total_budget?.toLocaleString() || 0}
                            </span>
                          </div>
                        </ComboboxItem>
                      ))}
                    </ComboboxGroup>
                  </ComboboxList>
                </ComboboxCommand>
              </ComboboxContent>
            </Combobox>
          </div>

          {/* Financial Details */}
          <div className="space-y-4">
            <Label className="flex items-center gap-2 text-base font-semibold">
              <DollarSign className="h-4 w-4" />
              Financial Details
            </Label>

            {/* Total Value */}
            <div className="space-y-2">
              <Label htmlFor="total_value">
                Total Contract Value <span className="text-red-500">*</span>
              </Label>
              <Input
                id="total_value"
                type="number"
                min="0"
                step="0.01"
                value={formData.total_value}
                onChange={(e) => {
                  const totalValue = parseFloat(e.target.value) || 0;
                  setFormData((prev) => ({
                    ...prev,
                    total_value: totalValue,
                    remaining_value: totalValue - prev.paid_value,
                  }));
                }}
                placeholder="0.00"
                required
              />
              {selectedProposal && (
                <p className="text-sm text-blue-600">
                  Value inherited from proposal: $
                  {selectedProposal.total_budget?.toLocaleString() || 0}
                </p>
              )}
            </div>

            {/* Paid Value */}
            <div className="space-y-2">
              <Label htmlFor="paid_value">Paid Amount</Label>
              <Input
                id="paid_value"
                type="number"
                min="0"
                step="0.01"
                value={formData.paid_value}
                onChange={(e) => {
                  const paidValue = parseFloat(e.target.value) || 0;
                  setFormData((prev) => ({
                    ...prev,
                    paid_value: paidValue,
                    remaining_value: prev.total_value - paidValue,
                  }));
                }}
                placeholder="0.00"
              />
            </div>

            {/* Remaining Value (calculated) */}
            <div className="space-y-2">
              <Label htmlFor="remaining_value">Remaining Amount</Label>
              <Input
                id="remaining_value"
                type="number"
                value={formData.remaining_value}
                placeholder="0.00"
                disabled
                className="bg-gray-50 dark:bg-gray-800"
              />
              <p className="text-sm text-gray-500">
                Automatically calculated: Total Value - Paid Amount
              </p>
            </div>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Start Date <span className="text-red-500">*</span>
              </Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    start_date: e.target.value,
                  }))
                }
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                End Date <span className="text-red-500">*</span>
              </Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, end_date: e.target.value }))
                }
                required
              />
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value: any) =>
                setFormData((prev) => ({ ...prev, status: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="terminated">Terminated</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Document Upload */}
          <div className="space-y-2">
            <Label htmlFor="documents" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Contract Documents
            </Label>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
              <input
                id="documents"
                type="file"
                multiple
                onChange={handleFileSelect}
                className="hidden"
                accept=".pdf,.doc,.docx,.txt"
              />
              <label
                htmlFor="documents"
                className="cursor-pointer flex flex-col items-center justify-center space-y-2"
              >
                <Upload className="h-8 w-8 text-gray-400" />
                <span className="text-sm text-gray-500">
                  Click to upload contract documents
                </span>
                <span className="text-xs text-gray-400">
                  PDF, DOC, DOCX, TXT files supported
                </span>
              </label>
            </div>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Files:</Label>
                <div className="space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded"
                    >
                      <div className="flex items-center space-x-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm">{file.name}</span>
                        <span className="text-xs text-gray-500">
                          ({(file.size / 1024).toFixed(1)} KB)
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Creating..." : "Create Contract"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
