"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Tit<PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/common/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { Textarea } from "@/components/common/ui/textarea";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";
import RichTextEditor from "@/components/common/richtext";

interface ContractEditDialogProps {
  contract: ApiContract;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: Partial<ApiContract>) => void;
  isUpdating: boolean;
}

const statusOptions = [
  { value: "draft", label: "Draft" },
  { value: "active", label: "Active" },
  { value: "completed", label: "Completed" },
  { value: "terminated", label: "Terminated" },
  { value: "expired", label: "Expired" },
];

export function ContractEditDialog({
  contract,
  isOpen,
  onOpenChange,
  onSubmit,
  isUpdating,
}: ContractEditDialogProps) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    status: "draft" as const,
    client_id: "",
    total_value: 0,
    paid_value: 0,
    remaining_value: 0,
    start_date: "",
    end_date: "",
  });

  // Initialize form data when contract changes
  useEffect(() => {
    if (contract) {
      setFormData({
        title: contract.title || "",
        description: contract.description || "",
        status: contract.status,
        client_id: contract.client_id || "",
        total_value: contract.total_value || 0,
        paid_value: contract.paid_value || 0,
        remaining_value: contract.remaining_value || 0,
        start_date: contract.start_date
          ? contract.start_date.split("T")[0]
          : "",
        end_date: contract.end_date ? contract.end_date.split("T")[0] : "",
      });
    }
  }, [contract]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      start_date: formData.start_date + "T00:00:00.000Z",
      end_date: formData.end_date + "T23:59:59.999Z",
    });
  };

  const handleInputChange = (field: string, value: unknown) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Contract</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Contract Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter contract title"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="client_id">Client ID *</Label>
              <Input
                id="client_id"
                value={formData.client_id}
                onChange={(e) => handleInputChange("client_id", e.target.value)}
                placeholder="Enter client ID"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <RichTextEditor
                id="contract-description"
                value={formData.description}
                onChange={(content) =>
                  handleInputChange("description", content)
                }
                placeholder="Enter contract description"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Financial Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Financial Information</h3>

            <div className="space-y-2">
              <Label htmlFor="total_value">Total Contract Value *</Label>
              <Input
                id="total_value"
                type="number"
                min="0"
                step="0.01"
                value={formData.total_value}
                onChange={(e) => {
                  const totalValue = parseFloat(e.target.value) || 0;
                  setFormData((prev) => ({
                    ...prev,
                    total_value: totalValue,
                    remaining_value: totalValue - prev.paid_value,
                  }));
                }}
                placeholder="0.00"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="paid_value">Paid Amount</Label>
              <Input
                id="paid_value"
                type="number"
                min="0"
                step="0.01"
                value={formData.paid_value}
                onChange={(e) => {
                  const paidValue = parseFloat(e.target.value) || 0;
                  setFormData((prev) => ({
                    ...prev,
                    paid_value: paidValue,
                    remaining_value: prev.total_value - paidValue,
                  }));
                }}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="remaining_value">Remaining Amount</Label>
              <Input
                id="remaining_value"
                type="number"
                value={formData.remaining_value}
                placeholder="0.00"
                disabled
                className="bg-gray-50 dark:bg-gray-800"
              />
              <p className="text-sm text-gray-500">
                Automatically calculated: Total Value - Paid Amount
              </p>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Contract Timeline</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date *</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) =>
                    handleInputChange("start_date", e.target.value)
                  }
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_date">End Date *</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) =>
                    handleInputChange("end_date", e.target.value)
                  }
                  required
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? "Updating..." : "Update Contract"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
