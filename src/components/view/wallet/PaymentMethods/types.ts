// Types for payment methods
export interface PaymentMethod {
  id: string;
  type:
    | "credit_card"
    | "debit_card"
    | "paypal"
    | "bank_account"
    | "apple_pay"
    | "google_pay";
  provider:
    | "visa"
    | "mastercard"
    | "amex"
    | "discover"
    | "paypal"
    | "bank"
    | "apple"
    | "google";
  last4?: string;
  cvv?: string;
  expiryMonth?: string;
  expiryYear?: string;
  holderName: string;
  isDefault: boolean;
  isVerified: boolean;
  email?: string; // For PayPal
  bankName?: string; // For bank accounts
  accountType?: "checking" | "savings"; // For bank accounts
  createdAt: string;
}
