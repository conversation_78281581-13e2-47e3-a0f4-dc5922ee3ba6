import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  fetchDashboardSummary,
  fetchRecentContracts,
  fetchRecentProposals,
  fetchDashboardStatistics,
  type DashboardSummary,
} from "../actions/dashboard";

// Dashboard state interface
export interface DashboardState {
  summary: DashboardSummary | null;
  recentContracts: any[];
  recentProposals: any[];
  contractStatistics: any;
  proposalStatistics: any;
  isLoading: boolean;
  isLoadingContracts: boolean;
  isLoadingProposals: boolean;
  isLoadingStatistics: boolean;
  error: string | null;
}

// Initial state
const initialState: DashboardState = {
  summary: null,
  recentContracts: [],
  recentProposals: [],
  contractStatistics: null,
  proposalStatistics: null,
  isLoading: false,
  isLoadingContracts: false,
  isLoadingProposals: false,
  isLoadingStatistics: false,
  error: null,
};

// Create the dashboard slice
const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Set error
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    // Clear dashboard data
    clearDashboard: (state) => {
      state.summary = null;
      state.recentContracts = [];
      state.recentProposals = [];
      state.contractStatistics = null;
      state.proposalStatistics = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch dashboard summary
    builder
      .addCase(fetchDashboardSummary.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboardSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.summary = action.payload;
        state.recentContracts = action.payload.recentContracts;
        state.recentProposals = action.payload.recentProposals;
        state.contractStatistics = action.payload.contractStatistics;
        state.proposalStatistics = action.payload.proposalStatistics;
        state.error = null;
      })
      .addCase(fetchDashboardSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch recent contracts
    builder
      .addCase(fetchRecentContracts.pending, (state) => {
        state.isLoadingContracts = true;
        state.error = null;
      })
      .addCase(fetchRecentContracts.fulfilled, (state, action) => {
        state.isLoadingContracts = false;
        state.recentContracts = action.payload;
        state.error = null;
      })
      .addCase(fetchRecentContracts.rejected, (state, action) => {
        state.isLoadingContracts = false;
        state.error = action.payload as string;
      });

    // Fetch recent proposals
    builder
      .addCase(fetchRecentProposals.pending, (state) => {
        state.isLoadingProposals = true;
        state.error = null;
      })
      .addCase(fetchRecentProposals.fulfilled, (state, action) => {
        state.isLoadingProposals = false;
        state.recentProposals = action.payload;
        state.error = null;
      })
      .addCase(fetchRecentProposals.rejected, (state, action) => {
        state.isLoadingProposals = false;
        state.error = action.payload as string;
      });

    // Fetch dashboard statistics
    builder
      .addCase(fetchDashboardStatistics.pending, (state) => {
        state.isLoadingStatistics = true;
        state.error = null;
      })
      .addCase(fetchDashboardStatistics.fulfilled, (state, action) => {
        state.isLoadingStatistics = false;
        state.contractStatistics = action.payload.contractStatistics;
        state.proposalStatistics = action.payload.proposalStatistics;
        state.error = null;
      })
      .addCase(fetchDashboardStatistics.rejected, (state, action) => {
        state.isLoadingStatistics = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { clearError, setError, clearDashboard } = dashboardSlice.actions;

// Export selectors
export const selectDashboardSummary = (state: { dashboard: DashboardState }) =>
  state.dashboard.summary;
export const selectRecentContracts = (state: { dashboard: DashboardState }) =>
  state.dashboard.recentContracts;
export const selectRecentProposals = (state: { dashboard: DashboardState }) =>
  state.dashboard.recentProposals;
export const selectContractStatistics = (state: { dashboard: DashboardState }) =>
  state.dashboard.contractStatistics;
export const selectProposalStatistics = (state: { dashboard: DashboardState }) =>
  state.dashboard.proposalStatistics;
export const selectIsLoading = (state: { dashboard: DashboardState }) =>
  state.dashboard.isLoading;
export const selectError = (state: { dashboard: DashboardState }) =>
  state.dashboard.error;

// Export reducer
export default dashboardSlice.reducer;
