import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import { toast } from "sonner";
import type {
  PaymentMethod,
  CreatePaymentMethod,
  UpdatePaymentMethod,
  SetDefaultPaymentMethod,
} from "@/lib/api/validators/payment";

export interface PaymentMethodsResponse {
  success: boolean;
  error: boolean;
  data: PaymentMethod[];
  message?: string;
  statusCode?: number;
  timestamp?: string;
}

export interface PaymentMethodResponse {
  success: boolean;
  error: boolean;
  data: PaymentMethod;
  message?: string;
  statusCode?: number;
  timestamp?: string;
}

export interface PaymentMethodStatistics {
  total: number;
  byType: {
    visa: number;
    mastercard: number;
    amex: number;
    discover: number;
    paypal: number;
    bank: number;
    apple: number;
    google: number;
  };
}

export interface PaymentMethodStatisticsResponse {
  success: boolean;
  error: boolean;
  data: PaymentMethodStatistics;
  message?: string;
  statusCode?: number;
  timestamp?: string;
}

export interface SearchPaymentMethodsResponse {
  success: boolean;
  error: boolean;
  data: {
    query: string;
    results: PaymentMethod[];
    count: number;
  };
  message?: string;
  statusCode?: number;
  timestamp?: string;
}

export interface BulkDeleteResponse {
  success: boolean;
  error: boolean;
  data: {
    deletedCount: number;
    message: string;
  };
  message?: string;
  statusCode?: number;
  timestamp?: string;
}

// Fetch all payment methods
export const fetchPaymentMethods = createAsyncThunk(
  "payment/fetchPaymentMethods",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<PaymentMethodsResponse>("payment");

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch payment methods");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch payment methods",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch payment methods";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single payment method
export const fetchPaymentMethod = createAsyncThunk(
  "payment/fetchPaymentMethod",
  async (paymentMethodId: string, { rejectWithValue }) => {
    try {
      const response = await api.get<PaymentMethodResponse>(
        `payment/${paymentMethodId}`
      );

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch payment method");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch payment method",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch payment method";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create payment method
export const createPaymentMethod = createAsyncThunk(
  "payment/createPaymentMethod",
  async (paymentMethodData: CreatePaymentMethod, { rejectWithValue }) => {
    try {
      const response = await api.post<PaymentMethodResponse>(
        "payment",
        paymentMethodData
      );

      if (response.success && !response.error) {
        toast.success(
          response.message || "Payment method created successfully"
        );
        return response.data;
      } else {
        toast.error(response.message || "Failed to create payment method");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create payment method",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create payment method";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update payment method
export const updatePaymentMethod = createAsyncThunk(
  "payment/updatePaymentMethod",
  async (
    { id, data }: { id: string; data: UpdatePaymentMethod },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.patch<PaymentMethodResponse>(
        `payment/${id}`,
        data
      );

      if (response.success && !response.error) {
        toast.success(
          response.message || "Payment method updated successfully"
        );
        return response.data;
      } else {
        toast.error(response.message || "Failed to update payment method");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update payment method",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update payment method";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Set default payment method
export const setDefaultPaymentMethod = createAsyncThunk(
  "payment/setDefaultPaymentMethod",
  async (paymentMethodId: string, { rejectWithValue }) => {
    try {
      const response = await api.post<PaymentMethodResponse>(
        `payment/${paymentMethodId}/default`
      );

      if (response.success && !response.error) {
        toast.success(
          response.message || "Default payment method updated successfully"
        );
        return response.data;
      } else {
        toast.error(response.message || "Failed to set default payment method");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to set default payment method",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to set default payment method";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete payment method
export const deletePaymentMethod = createAsyncThunk(
  "payment/deletePaymentMethod",
  async (paymentMethodId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<PaymentMethodResponse>(
        `payment/${paymentMethodId}`
      );

      if (response.success && !response.error) {
        toast.success(
          response.message || "Payment method deleted successfully"
        );
        return response.data;
      } else {
        toast.error(response.message || "Failed to delete payment method");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete payment method",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to delete payment method";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Bulk delete payment methods
export const bulkDeletePaymentMethods = createAsyncThunk(
  "payment/bulkDeletePaymentMethods",
  async (paymentMethodIds: string[], { rejectWithValue }) => {
    try {
      const response = await api.delete<BulkDeleteResponse>("payment", {
        ids: paymentMethodIds,
      });

      if (response.success && !response.error) {
        toast.success(
          response.message || "Payment methods deleted successfully"
        );
        return { ids: paymentMethodIds, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete payment methods");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete payment methods",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to delete payment methods";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch payment method statistics
export const fetchPaymentMethodStatistics = createAsyncThunk(
  "payment/fetchPaymentMethodStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<PaymentMethodStatisticsResponse>(
        "payment?stats=true"
      );

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(
          response.message || "Failed to fetch payment method statistics"
        );
        return rejectWithValue({
          success: false,
          error: true,
          message:
            response.message || "Failed to fetch payment method statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch payment method statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Search payment methods
export const searchPaymentMethods = createAsyncThunk(
  "payment/searchPaymentMethods",
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await api.get<SearchPaymentMethodsResponse>(
        `payment?search=${encodeURIComponent(query)}`
      );

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to search payment methods");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to search payment methods",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to search payment methods";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch payment methods by type
export const fetchPaymentMethodsByType = createAsyncThunk(
  "payment/fetchPaymentMethodsByType",
  async (type: string, { rejectWithValue }) => {
    try {
      const response = await api.get<PaymentMethodsResponse>(
        `payment?type=${encodeURIComponent(type)}`
      );

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(
          response.message || "Failed to fetch payment methods by type"
        );
        return rejectWithValue({
          success: false,
          error: true,
          message:
            response.message || "Failed to fetch payment methods by type",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch payment methods by type";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Validate payment method data
export const validatePaymentMethodData = createAsyncThunk(
  "payment/validatePaymentMethodData",
  async (paymentMethodData: CreatePaymentMethod, { rejectWithValue }) => {
    try {
      const response = await api.post<{
        success: boolean;
        error: boolean;
        data: {
          isValid: boolean;
          data: CreatePaymentMethod;
          message: string;
        };
        message?: string;
        statusCode?: number;
        timestamp?: string;
      }>("payment", { ...paymentMethodData, validate: true });

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Validation failed",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Validation failed";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
