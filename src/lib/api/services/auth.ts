import { BaseService, ServiceResponse } from "./base";
import { prisma } from "@/lib/common/prisma";
import { CryptoMiddleware } from "@/lib/crypto/middleware";
import { DocumentService } from "./document";
import { getSignedURL } from "@/lib/common/s3";
import { BraintreeService } from "./braintree";
import { Brain } from "lucide-react";

/**
 * Authentication service configuration
 */
export interface AuthServiceConfig {
  enableLogging?: boolean;
  throwOnError?: boolean;
}

/**
 * Authentication service for handling login operations
 *
 * This service extends BaseService and provides:
 * 1. Email/password authentication with database validation
 * 2. Password matching against stored user credentials
 * 3. Standardized error handling and logging
 */
export class AuthService extends BaseService {
  constructor(config: AuthServiceConfig = {}) {
    super(config);
    this.setModel(prisma.user as any);
  }

  /**
   * Authenticate user with email and password
   * @param email - User email address
   * @param password - User password
   * @returns Service response with authentication result
   */
  async login(email: string, password: string): Promise<ServiceResponse> {
    try {
      this.log("info", `Attempting login for email: ${email}`);

      // Find user by email
      const user: any = await this.findUniqueRecord({
        where: { email },
        select: {
          id: true,
          name: true,
          email: true,
          customerId: true,
          image: true,
          profile: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          accounts: {
            select: {
              id: true,
              password: true,
              payment_method: {
                select: {
                  id: true,
                  vault_id: true,
                  holder_name: true,
                  ref_number: true,
                  expiry_month: true,
                  expiry_year: true,
                  cvv: true,
                  type: true,
                  isDefault: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        this.log("warn", `User not found for email: ${email}`);
        return this.createErrorResponse(
          `User not found for email: ${email}`,
          401
        );
      }

      const storedPassword = user?.accounts[0]?.password;

      // Check if user has password in accounts
      if (!storedPassword) {
        this.log("warn", `No password found for user: ${email}`);
        return this.createErrorResponse(
          `No password found for user: ${email}`,
          401
        );
      }

      const [hashedPassword, salt] = storedPassword.split("==");

      const valid = await CryptoMiddleware.verifyPassword(
        password,
        hashedPassword,
        salt
      );

      // Match password
      if (!valid) {
        this.log("warn", `Password mismatch for user: ${email}`);
        return this.createErrorResponse(
          `Password mismatch for user: ${email}`,
          401
        );
      }

      // Get Avatar Signed URL
      let signedURL = await getSignedURL(user.image);
      user.image = signedURL;

      // Extract profile from array
      if (user.profile?.length > 0) user.profile = user.profile?.[0];
      if (user.accounts?.length > 0) user.account = user.accounts?.[0];
      if (user.account?.payment_method?.length > 0)
        user.payment_methods = user.account?.payment_method;

      delete user.accounts;
      delete user.account.password;
      delete user.account.payment_method;

      this.log("info", `Login successful for user: ${email}`);

      return this.createSuccessResponse(user, 200, "Login successful");
    } catch (error) {
      this.log("error", `Login error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during login",
        500
      );
    }
  }

  async register(
    name: string,
    email: string,
    password: string
  ): Promise<ServiceResponse> {
    try {
      this.log("info", `Attempting registration for email: ${email}`);

      if (!name) {
        this.log("warn", `Name is required for registration`);
        return this.createErrorResponse("Name is required", 400);
      }

      if (!email) {
        this.log("warn", `Email is required for registration`);
        return this.createErrorResponse("Email is required", 400);
      }

      if (!password) {
        this.log("warn", `Password is required for registration`);
        return this.createErrorResponse("Password is required", 400);
      }

      // Check if user already exists
      const existingUser = await this.findUniqueRecord({
        where: { email },
      });

      if (existingUser) {
        this.log("warn", `User already exists for email: ${email}`);
        return this.createErrorResponse("User already exists", 409);
      }

      // Password Encryption
      const { hashedPassword, salt } = await CryptoMiddleware.hashPassword(
        password
      );

      // Register client
      let gateway = new BraintreeService({
        requireAuth: false,
      });

      // Customer Names
      let firstName: string = name.split(" ")[0];
      let lastName: string = name.split(" ")[1] ?? "..";

      console.log("\nCreating customer", name, email, firstName, lastName);

      // Log Customer on registration
      let braintree = await gateway.createCustomer({
        firstName,
        lastName,
        email,
      });

      // Create user
      const user = await this.createRecord({
        data: {
          name,
          email,
          customerId: braintree.data.id,
          accounts: {
            create: {
              password: hashedPassword + "==" + salt,
              type: "credentials",
              provider: "credentials",
              providerAccountId: email,
              refresh_token: null,
              access_token: null,
              expires_at: null,
              token_type: null,
              scope: null,
              id_token: null,
              session_state: null,
            },
          },
        },
      });

      this.log("info", `Registration successful for user: ${email}`);

      return this.createSuccessResponse(user, 201, "Registration successful");
    } catch (error) {
      this.log("error", `Registration error: ${error}`);
      return this.createErrorResponse(
        "An unexpected error occurred during registration",
        500
      );
    }
  }

  async updateAvatar(id: string, avatar: File): Promise<ServiceResponse> {
    try {
      let document = new DocumentService({
        requireAuth: false,
      });

      let uploadedFile = await document.uploadDocument(avatar, "avatars", {
        category: "avatar",
        association_entity: "user",
        association_id: id,
      });

      let user = await this.updateRecord({
        where: { id: id },
        data: { image: uploadedFile.data.path },
        select: { image: true },
      });

      return this.createSuccessResponse(
        user,
        200,
        "Avatar updated successfully"
      );
    } catch (error: any) {
      return this.createErrorResponse(error.message, 500);
    }
  }
}
