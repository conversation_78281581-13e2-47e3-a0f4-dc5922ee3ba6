import { createAsyncThunk } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { api } from "@/lib/common/requests";
import type {
  CreateRoom,
  CreateMessage,
  UserState,
} from "@/lib/api/validators/schemas/chat";

// Note: fetchRooms is now handled by SWR in useChat hook

// Note: fetchMessages is now handled by SWR in useChat hook

// Send a message
export const sendMessage = createAsyncThunk(
  "chat/sendMessage",
  async (
    {
      roomId,
      data,
      withAttachments,
    }: { roomId: string; data: any; withAttachments: boolean },
    { rejectWithValue }
  ) => {
    try {
      const URL = `chat/room/${roomId}/messages`;
      // Check if API endpoint exists - for now, show API required message
      if (withAttachments) {
        await api.upload(URL + "?withAttachments=true", data);
      } else {
        await api.post(URL, data);
      }
      return data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to send message";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create a new room
export const createRoom = createAsyncThunk(
  "chat/createRoom",
  async (roomData: CreateRoom, { rejectWithValue }) => {
    try {
      const response = await api.post("chat/room", roomData);

      if (response.success) {
        toast.success(response.message || "Room created successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to create room");
        return rejectWithValue(response);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create room";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update an existing room
export const updateRoom = createAsyncThunk(
  "chat/updateRoom",
  async (
    { id, roomData }: { id: string; roomData: Partial<CreateRoom> },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put(`chat/room/${id}`, roomData);

      if (response.success) {
        toast.success(response.message || "Room updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update room");
        return rejectWithValue(response);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update room";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete a room
export const deleteRoom = createAsyncThunk(
  "chat/deleteRoom",
  async (roomId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete(`chat/room/${roomId}`);

      if (response.success) {
        toast.success(response.message || "Room deleted successfully");
        return { id: roomId, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete room");
        return rejectWithValue(response);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete room";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update a message
export const updateMessage = createAsyncThunk(
  "chat/updateMessage",
  async (
    { messageId, content }: { messageId: string; content: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put(`chat/message/${messageId}`, { content });

      if (response.success) {
        toast.success(response.message || "Message updated successfully");
        return response.data;
      } else {
        toast.error(response.message || "Failed to update message");
        return rejectWithValue(response);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update message";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete a message
export const deleteMessage = createAsyncThunk(
  "chat/deleteMessage",
  async (messageId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete(`chat/message/${messageId}`);

      if (response.success) {
        toast.success(response.message || "Message deleted successfully");
        return { id: messageId, ...response.data };
      } else {
        toast.error(response.message || "Failed to delete message");
        return rejectWithValue(response);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete message";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update user state (online/offline/typing/away)
export const updateUserState = createAsyncThunk(
  "chat/updateUserState",
  async (
    {
      userId,
      state,
      roomId,
    }: { userId: string; state: UserState; roomId?: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await api.put("chat/user-state", {
        userId,
        state,
        roomId,
      });

      if (response.success && !response.error) {
        return response.data;
      } else {
        // Don't show toast for user state updates to avoid spam
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update user state",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update user state";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Get typing users for a room
export const fetchTypingUsers = createAsyncThunk(
  "chat/fetchTypingUsers",
  async (_roomId: string, { rejectWithValue }) => {
    try {
      // This functionality requires an API endpoint
      toast.error("This requires an API");
      return rejectWithValue({
        success: false,
        error: true,
        message: "This requires an API",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch typing users";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch chat statistics
export const fetchChatStatistics = createAsyncThunk(
  "chat/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get("chat/statistics");

      if (response.success && !response.error) {
        return response.data;
      } else {
        toast.error(response.message || "Failed to fetch chat statistics");
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch chat statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch chat statistics";
      toast.error(errorMessage);
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
