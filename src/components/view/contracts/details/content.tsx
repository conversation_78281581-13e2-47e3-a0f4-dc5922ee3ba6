"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from "@/components/common/ui/card";
import { Badge } from "@/components/common/ui/badge";
import { Separator } from "@/components/common/ui/separator";
import {
  Calendar,
  DollarSign,
  FileText,
  Clock,
  CheckCircle,
} from "lucide-react";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";

interface ContractDetailsContentProps {
  contract: ApiContract;
}

export function ContractDetailsContent({
  contract,
}: ContractDetailsContentProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getDaysRemaining = () => {
    const endDate = new Date(contract.end_date);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getContractDuration = () => {
    const startDate = new Date(contract.start_date);
    const endDate = new Date(contract.end_date);
    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const statusColors = {
    draft: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
    active: "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
    completed: "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
    terminated: "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
    expired:
      "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-300",
  };

  return (
    <div className="space-y-6">
      {/* Financial Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Financial Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Contract Value
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(contract.total_value || 0)}
              </div>
            </div>
            <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Paid Amount
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(contract.paid_value || 0)}
              </div>
            </div>
            <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Remaining Amount
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(contract.remaining_value || 0)}
              </div>
            </div>
            <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Daily Rate
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(
                  (contract.total_value || 0) / getContractDuration()
                )}
              </div>
            </div>
            <div className="rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Payment Status
              </div>
              <div className="mt-1">
                <Badge
                  variant="secondary"
                  className="bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300"
                >
                  Pending
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contract Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Contract Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Contract Title
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {contract.title}
                </p>
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Client Name
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {contract.client_id}
                </p>
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Status
                </label>
                <div className="mt-1">
                  <Badge
                    variant="secondary"
                    className={
                      statusColors[contract.status as keyof typeof statusColors]
                    }
                  >
                    {contract.status.charAt(0).toUpperCase() +
                      contract.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Duration
                </label>
                <p className="mt-1 text-sm text-gray-900 dark:text-white">
                  {getContractDuration()} days
                </p>
              </div>
              {contract.status === "active" && (
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Days Remaining
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {getDaysRemaining()} days
                  </p>
                </div>
              )}
            </div>
          </div>

          {contract.description && (
            <>
              <Separator />
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Description
                </label>
                <div
                  className="mt-2 prose prose-sm max-w-none dark:prose-invert"
                  dangerouslySetInnerHTML={{ __html: contract.description }}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Contract Timeline</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Contract Start Date
                </p>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(contract.start_date)}
              </p>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                  <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Contract End Date
                </p>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(contract.end_date)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
