import { z } from "zod";

/**
 * Payment Method validation schemas
 *
 * These schemas define the structure and validation rules for payment method data
 * used across the API endpoints and services.
 */

// Payment Method Types based on Prisma schema
export enum PaymentMethodType {
  VISA = "visa",
  MASTERCARD = "mastercard",
  AMEX = "amex",
  DISCOVER = "discover",
  PAYPAL = "paypal",
  BANK = "bank",
  APPLE = "apple",
  GOOGLE = "google",
}

// Zod schemas for validation
export const CreatePaymentMethodSchema = z.object({
  holder_name: z.string().min(1, "Cardholder name is required"),
  ref_number: z
    .string()
    .min(4, "Reference number must be at least 4 characters"),
  expiry_month: z
    .string()
    .regex(/^(0[1-9]|1[0-2])$/, "Expiry month must be in MM format (01-12)"),
  expiry_year: z
    .string()
    .regex(/^\d{4}$/, "Expiry year must be in YYYY format"),
  cvv: z
    .string()
    .min(3, "CVV must be at least 3 digits")
    .max(4, "CVV must be at most 4 digits"),
  type: z
    .enum([
      "visa",
      "mastercard",
      "amex",
      "discover",
      "paypal",
      "bank",
      "apple",
      "google",
    ])
    .default("visa"),
  isDefault: z.boolean().default(false),
});

export const UpdatePaymentMethodSchema = CreatePaymentMethodSchema.partial();

export const PaymentMethodResponseSchema = z.object({
  id: z.string(),
  holder_name: z.string(),
  ref_number: z.string(),
  expiry_month: z.string(),
  expiry_year: z.string(),
  cvv: z.string(),
  type: z.enum([
    "visa",
    "mastercard",
    "amex",
    "discover",
    "paypal",
    "bank",
    "apple",
    "google",
  ]),
  isDefault: z.boolean(),
  accountId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Types
export type CreatePaymentMethod = z.infer<typeof CreatePaymentMethodSchema>;
export type UpdatePaymentMethod = z.infer<typeof UpdatePaymentMethodSchema>;
export type PaymentMethod = z.infer<typeof PaymentMethodResponseSchema>;

/**
 * Additional validation schemas for specific use cases
 */

// Schema for payment method search/filtering
export const PaymentMethodFilterSchema = z.object({
  type: z
    .enum([
      "visa",
      "mastercard",
      "amex",
      "discover",
      "paypal",
      "bank",
      "apple",
      "google",
    ])
    .optional(),
  search: z.string().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  offset: z.coerce.number().int().min(0).optional(),
});

// Schema for bulk operations
export const BulkDeletePaymentMethodsSchema = z.object({
  ids: z.array(z.string().min(1)).min(1, "At least one ID is required"),
});

// Schema for payment method validation (without saving)
export const ValidatePaymentMethodSchema = CreatePaymentMethodSchema.extend({
  validate: z.literal(true),
});

// Schema for setting default payment method
export const SetDefaultPaymentMethodSchema = z.object({
  id: z.string().min(1, "Payment method ID is required"),
});

export type PaymentMethodFilter = z.infer<typeof PaymentMethodFilterSchema>;
export type BulkDeletePaymentMethods = z.infer<
  typeof BulkDeletePaymentMethodsSchema
>;
export type ValidatePaymentMethod = z.infer<typeof ValidatePaymentMethodSchema>;
export type SetDefaultPaymentMethod = z.infer<
  typeof SetDefaultPaymentMethodSchema
>;
