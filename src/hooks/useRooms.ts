"use client";

import { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import useS<PERSON> from "swr";
import { fetcher } from "@/lib/common/requests";
import type { RootState, AppDispatch } from "@/store";
import { createRoom, updateRoom, deleteRoom } from "@/store/actions/chat";
import { setCurrentRoom } from "@/store/slices/chat";
import type { CreateRoom } from "@/lib/api/validators/schemas/chat";
import { useAuth } from "./useAuth";

export function useRooms() {
  const dispatch = useDispatch<AppDispatch>();

  const { user } = useAuth();
  const { currentRoomId, isCreatingRoom } = useSelector(
    (state: RootState) => state.chat
  );

  // Use SWR for fetching rooms
  const {
    data: roomsData = [],
    error: roomsError,
    mutate: mutateRooms,
  } = useSWR(
    user?.id ? `chat/room?userId=${user.id}&includeMembers=true` : null,
    fetcher
  );

  const rooms = roomsData?.data?.rooms || [];

  // Create a new room
  const createChatRoom = useCallback(
    async (roomData: CreateRoom) => {
      try {
        const newRoom = await dispatch(createRoom(roomData)).unwrap();
        // Automatically switch to the new room
        dispatch(setCurrentRoom(newRoom.id));
        // Refresh rooms list
        await mutateRooms();
        return newRoom;
      } catch (error) {
        console.error("Failed to create room:", error);
        throw error;
      }
    },
    [dispatch, mutateRooms]
  );

  // Update an existing room
  const updateChatRoom = useCallback(
    async (roomId: string, roomData: Partial<CreateRoom>) => {
      try {
        const updatedRoom = await dispatch(
          updateRoom({ id: roomId, roomData })
        ).unwrap();
        // Refresh rooms list to show updated data
        await mutateRooms();
        return updatedRoom;
      } catch (error) {
        console.error("Failed to update room:", error);
        throw error;
      }
    },
    [dispatch, mutateRooms]
  );

  // Delete a room
  const deleteChatRoom = useCallback(
    async (roomId: string) => {
      try {
        await dispatch(deleteRoom(roomId)).unwrap();

        // If the deleted room was the current room, clear current room
        if (currentRoomId === roomId) {
          dispatch(setCurrentRoom(null));
        }

        // Refresh rooms list
        await mutateRooms();
        return { success: true, message: "Room deleted successfully" };
      } catch (error) {
        console.error("Failed to delete room:", error);
        throw error;
      }
    },
    [dispatch, currentRoomId, mutateRooms]
  );

  return {
    rooms,
    isCreatingRoom,
    error: roomsError,
    isLoading: !roomsData,
    mutateRooms: mutateRooms,
    createRoom: createChatRoom,
    updateRoom: updateChatRoom,
    deleteRoom: deleteChatRoom,
  };
}
